package com.mercaso.data.master_catalog.service;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import java.time.Instant;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

public interface MasterCatalogBatchJobService {

  void startInitialProcessing(Integer batchSize);

  MasterCatalogBatchJobDto searchDetailById(UUID jobId);

  Page<MasterCatalogBatchJobListDto> search(String jobNumber, Instant createdStartDate, Instant createdEndDate, Instant completedStartDate, Instant completedEndDate, PageRequest pageRequest);
}
