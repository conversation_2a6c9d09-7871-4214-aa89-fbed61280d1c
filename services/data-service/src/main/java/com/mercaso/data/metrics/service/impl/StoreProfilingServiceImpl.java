package com.mercaso.data.metrics.service.impl;

import com.mercaso.data.metrics.adapter.ItemManagementServiceClientAdaptor;
import com.mercaso.data.metrics.constant.OperationAreaZipCodeConstant;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDiscreteMetricsDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreSummaryDto;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreContinuousMetricsDto;
import com.mercaso.data.metrics.dto.StoreProfilingContinuousMetricsListDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreAroundInfoDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoExtDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreMetricsDto;
import com.mercaso.data.metrics.enums.ContinuousMetricsCategoryEnums;
import com.mercaso.data.metrics.enums.DiscreteMetricsCategoryEnums;
import com.mercaso.data.metrics.entity.StoreProfilingStoreContinuousMetricsEntity;
import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import com.mercaso.data.metrics.entity.StoreProfilingStoreMetricsEntity;
import com.mercaso.data.metrics.entity.StoreProfilingTagEntity;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreContinuousMetricsDtoMapper;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreInfoDtoMapper;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreMetricsDtoMapper;
import com.mercaso.data.metrics.repository.StoreProfilingStoreContinuousMetricsRepository;
import com.mercaso.data.metrics.repository.StoreProfilingStoreInfoRepository;
import com.mercaso.data.metrics.repository.StoreProfilingStoreMetricsRepository;
import com.mercaso.data.metrics.repository.StoreProfilingTagRepository;
import com.mercaso.data.metrics.repository.specification.StoreProfilingStoreInfoSpecifications;
import com.mercaso.data.metrics.repository.specification.StoreProfilingStoreMetricsSpecification;
import com.mercaso.data.metrics.service.StoreProfilingService;
import com.mercaso.data.metrics.utils.LuceneTokenizer;
import com.mercaso.data.metrics.utils.StoreAddressDtoHelper;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.val;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class StoreProfilingServiceImpl implements StoreProfilingService {

    private final StoreProfilingStoreMetricsRepository storeProfilingStoreMetricsRepository;

    private final StoreProfilingStoreInfoRepository storeProfilingStoreInfoRepository;

    private final StoreProfilingStoreContinuousMetricsRepository storeProfilingStoreContinuousMetricsRepository;

    private final StoreProfilingTagRepository storeProfilingTagRepository;

    private final StoreProfilingStoreMetricsDtoMapper storeProfilingStoreMetricsDtoMapper;

    private final StoreProfilingStoreInfoDtoMapper storeProfilingStoreInfoDtoMapper;

    private final StoreProfilingStoreContinuousMetricsDtoMapper storeProfilingStoreContinuousMetricsDtoMapper;

    private final ItemManagementServiceClientAdaptor itemManagementServiceClientAdaptor;

    private static final String MERCASO_STORE_SOURCE = "MERCASO";

    private static final List<String> AROUND_GRADE_METRIC_LIST = List.of(
        "Area Good for Families Grade",
        "Area Diversity Grade",
        "Area Jobs Grade",
        "Area Weather Grade",
        "Area Cost of Living Grade",
        "Area Health and Fitness Grade",
        "Area Outdoor Activities Grade",
        "Area Commute Grade");
    private static final List<String> AROUND_REAL_ESTATE_METRIC_LIST = List.of(
        "Area Feeling",
        "Median Home Value",
        "Median Rent");
    private static final List<String> AROUND_RESIDENT_DEMOGRAPHICS_METRIC_LIST = List.of(
        "Area Population",
        "Area Families with Children Percentage",
        "Area Unemployment Rate",
        "Area Poverty Rate",
        "Area Non-Citizens Percentage",
        "Area Median Household Income",
        "Area Majority Household Income Range",
        "Area Median Individual Income",
        "Area Majority Individual Income Range",
        "Area Majority Education Level",
        "Area Diversity Grade",
        "Area Male Percentage",
        "Area Female Percentage",
        "Area Majority Age Range");


    @Override
    public List<StoreProfilingStoreMetricsDto> findMetricsByStoreId(String storeId) {

        if (!StringUtils.hasText(storeId)) {
            return List.of();
        }

        List<StoreProfilingStoreMetricsEntity> storeProfilingStoreMetricsEntities = storeProfilingStoreMetricsRepository.findByStoreId(
            storeId);

        return storeProfilingStoreMetricsEntities.stream().map(storeProfilingStoreMetricsDtoMapper::toDto).toList();
    }

    @Override
    public StoreProfilingStoreInfoDto getStoreProfilingStoreInfoByStoreId(String storeId) {

        Optional<StoreProfilingStoreInfoEntity> storeInfoEntityOptional = storeProfilingStoreInfoRepository.findById(storeId);

        if (storeInfoEntityOptional.isEmpty()) {
            return null;
        }

        StoreProfilingStoreInfoEntity storeProfilingStoreInfoEntity = storeInfoEntityOptional.get();

        return storeProfilingStoreInfoDtoMapper.toDto(storeProfilingStoreInfoEntity);
    }


    @Override
    public StoreProfilingDto getStoreProfilingByStoreId(String storeId) {

        StoreProfilingStoreInfoDto storeInfoDto = getStoreProfilingStoreInfoByStoreId(storeId);
        List<StoreProfilingStoreMetricsDto> storeProfilingList = findMetricsByStoreId(storeId);

        // Sort by metricCategory and metricName in ascending order
        List<StoreProfilingStoreMetricsDto> sortedStoreProfilingList = storeProfilingList.stream()
            .sorted(Comparator.comparing(StoreProfilingStoreMetricsDto::getMetricCategory)
                .thenComparing(StoreProfilingStoreMetricsDto::getMetricName))
            .collect(Collectors.toList());

        return StoreProfilingDto.builder()
            .storeProfilingList(sortedStoreProfilingList)
            .storeInfo(storeInfoDto)
            .build();
    }

    @Override
    public Page<SearchStoreAddressDto> searchStoreAddressesForStoreProfiling(StoreAddressFilter filter) {

        Pageable pageable = PageRequest.of(filter.getPageable().getPageNumber(), filter.getPageable().getPageSize());

//         If no keyword is provided, return all addresses for the store
        if (org.apache.commons.lang3.StringUtils.isEmpty(filter.getKeyword())) {
            log.info("[searchStoreAddressesForStoreProfiling] No keyword provided, returning all addresses");
            return storeProfilingStoreInfoRepository.queryAll(pageable)
                .map(StoreAddressDtoHelper::buildStoreAddressDto);
        }

        Page<StoreProfilingStoreInfoEntity> addressInfoEntitiesPage = Page.empty();

        try {
            // Convert the keyword to a ts_query
            String formattedQuery = LuceneTokenizer.convertKeywordToTsQuery(filter.getKeyword());
            // Search for the keyword in the address_info table
            addressInfoEntitiesPage = storeProfilingStoreInfoRepository.findBySearchQuery(formattedQuery, pageable);
        } catch (Exception ex) {
            log.warn("[searchStoreAddressesForStoreProfiling] Failed to search for addresses by keyword: {}", ex.getMessage());
        }

        // If no results are found, use %like% search
        if (addressInfoEntitiesPage.isEmpty()) {
            log.info("[searchStoreAddressesForStoreProfiling] No results found, using %like% search");
            addressInfoEntitiesPage = searchByNamePartsForStoreProfiling(LuceneTokenizer.tokenize(filter.getKeyword()), pageable);
        }

        Page<SearchStoreAddressDto> storeAddressDtosPage = addressInfoEntitiesPage.map(StoreAddressDtoHelper::buildStoreAddressDto);
        log.info("[searchStoreAddressesForStoreProfiling] Found {} addresses", storeAddressDtosPage.getTotalElements());
        return storeAddressDtosPage;
    }


    public Page<StoreProfilingStoreInfoEntity> searchByNamePartsForStoreProfiling(List<String> parts, Pageable pageable) {
        Specification<StoreProfilingStoreInfoEntity> spec = Specification
            .where(StoreProfilingStoreInfoSpecifications.addressNameContainsParts(parts));
        return storeProfilingStoreInfoRepository.findAll(spec, pageable);
    }


    @Override
    public Page<StoreProfilingStoreInfoDto> searchStoreByTags(StoreAddressFilter filter) {

        // process the tags
        List<Pair<String, String>> tagsList = new ArrayList<>();
        if (StringUtils.hasText(filter.getTags())) {
            String[] tags = filter.getTags().split(",");
            for (String tag : tags) {
                if (!tag.contains("-")) {
                    throw new IllegalArgumentException("Tag format error, must contain '-' separator: " + tag);
                }
                String[] parts = tag.split("-", 2);
                tagsList.add(Pair.of(parts[0], parts[1]));
            }
        }

        // process the match
        String match = filter.getMatch();
        if (StringUtils.hasText(match) && !match.equals("all") && !match.equals("any")) {
                throw new IllegalArgumentException("Match value must be 'all' or 'any', current value: " + match);
            }

        // Get pageable from filter
        Pageable pageable = PageRequest.of(filter.getPageable().getPageNumber(), filter.getPageable().getPageSize());

        Page<String> entities = storeProfilingStoreMetricsRepository.findDistinctStoreIds(
                StoreProfilingStoreMetricsSpecification.matchTags(tagsList, match),
                pageable
        );

        // Get store info by store ids
        List<String> storeIds = entities.stream().toList();
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = storeProfilingStoreInfoRepository.findAllById(storeIds);
        // Return the page of store info DTOs
        return new PageImpl<>(storeInfoEntities.stream().map(storeProfilingStoreInfoDtoMapper::toDto).toList(), pageable, entities.getTotalElements());
    }

    @Override
    public List<String> getStoreTags() {

        List<StoreProfilingTagEntity> storeProfilingTags =  storeProfilingTagRepository.findAll();
        
        log.info("[getStoreTags] Found {} tags", storeProfilingTags.size());

        if (storeProfilingTags.isEmpty()) {
            return List.of();
        }


        return storeProfilingTags.stream().map(StoreProfilingTagEntity::getTag).distinct().toList();

    }

    @Override
    public List<String> findContinuousMetricNamesByStoreId(String storeId) {
        
        if (!StringUtils.hasText(storeId)) {
            log.warn("[findContinuousMetricNamesByStoreId] storeId is empty or null");
            return List.of();
        }

        List<String> metricNames = storeProfilingStoreContinuousMetricsRepository.findDistinctMetricNamesByStoreId(storeId);
        
        log.info("[findContinuousMetricNamesByStoreId] Found {} metric names for storeId: {}", metricNames.size(), storeId);

        return metricNames;
    }

    @Override
    public List<StoreProfilingStoreContinuousMetricsDto> findContinuousMetricsByStoreIdAndMetricName(String storeId, String metricName) {
       
        if (!StringUtils.hasText(storeId) || !StringUtils.hasText(metricName)) {
            log.warn("[findContinuousMetricsByStoreIdAndMetricName] storeId or metricName is empty or null. storeId: {}, metricName: {}", storeId, metricName);
            return List.of();
        }

        List<StoreProfilingStoreContinuousMetricsEntity> entities = storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName);
        
        log.info("[findContinuousMetricsByStoreIdAndMetricName] Found {} entities for storeId: {} and metricName: {}", 
            entities.size(), storeId, metricName);

        // Complete continuous data
        List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = fillContinuousData(entities);

        return storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(filledEntities);
    }

    @Override
    public List<StoreProfilingContinuousMetricsListDto> findContinuousMetricsListByStoreIdAndCategory(String storeId, ContinuousMetricsCategoryEnums category) {

        List<ContinuousMetricsCategoryEnums.MetricCategoryName> metricCategoryNameList = category.getMetricCategoryNameList();
        
        // Extract unique categories for batch query
        List<String> categories = metricCategoryNameList.stream()
            .map(ContinuousMetricsCategoryEnums.MetricCategoryName::getCategory)
            .distinct()
            .collect(Collectors.toList());
        
        
        // Batch query by categories
        List<StoreProfilingStoreContinuousMetricsEntity> allEntities = 
            storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, categories);
        
        // Filter by specific category+metricName combinations in memory
        List<StoreProfilingStoreContinuousMetricsEntity> entities = allEntities.stream()
            .filter(entity -> metricCategoryNameList.stream()
                .anyMatch(mcn -> mcn.getCategory().equals(entity.getMetricCategory()) 
                    && mcn.getMetricName().equals(entity.getMetricName())))
            .collect(Collectors.toList());
        
        log.info("[findContinuousMetricsListByStoreIdAndCategory] Found {} entities for storeId: {} and category: {}", 
            entities.size(), storeId, category.name());

        if (entities.isEmpty()) {
            return List.of();
        }

        // Group by metric name
        Map<String, List<StoreProfilingStoreContinuousMetricsEntity>> groupedByMetricName = entities.stream()
            .collect(Collectors.groupingBy(StoreProfilingStoreContinuousMetricsEntity::getMetricName));

        List<StoreProfilingContinuousMetricsListDto> result = new ArrayList<>();

        for (Map.Entry<String, List<StoreProfilingStoreContinuousMetricsEntity>> entry : groupedByMetricName.entrySet()) {
            List<StoreProfilingStoreContinuousMetricsEntity> metricEntities = entry.getValue();

            // Fill continuous data for this metric
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = fillContinuousData(metricEntities);

            if (!filledEntities.isEmpty()) {
                StoreProfilingStoreContinuousMetricsEntity firstEntity = filledEntities.get(0);
                
                // Convert entities to MetricValueDto list
                List<StoreProfilingContinuousMetricsListDto.MetricValueDto> metricValues = filledEntities.stream()
                    .map(entity -> new StoreProfilingContinuousMetricsListDto.MetricValueDto(
                        entity.getMetricValue().toString(),
                        entity.getMetricDate()
                    ))
                    .sorted(Comparator.comparing(StoreProfilingContinuousMetricsListDto.MetricValueDto::getDate).reversed()) // Sort by date descending
                    .collect(Collectors.toList());

                StoreProfilingContinuousMetricsListDto dto = new StoreProfilingContinuousMetricsListDto(
                    firstEntity.getStoreId(),
                    firstEntity.getMetricCategory(),
                    firstEntity.getMetricName(),
                    firstEntity.getMetricDesc(),
                    firstEntity.getMetricDateType(),
                    metricValues
                );

                result.add(dto);
            }
        }

        // Sort result by metric name
        result.sort(Comparator.comparing(StoreProfilingContinuousMetricsListDto::getMetricName));

        log.info("[findContinuousMetricsListByStoreIdAndCategory] Returning {} metric groups for storeId: {} and category: {}", 
            result.size(), storeId, category);

        return result;
    }

    private List<StoreProfilingStoreContinuousMetricsEntity> fillContinuousData(List<StoreProfilingStoreContinuousMetricsEntity> entities) {
        
        if (entities.isEmpty()) {
            return entities;
        }

        // Sort by date
        entities.sort((a, b) -> a.getMetricDate().compareTo(b.getMetricDate()));

        String dateType = entities.getFirst().getMetricDateType();

        // Determine time interval based on date type
        ChronoUnit timeUnit = getChronoUnit(dateType);
        if (timeUnit == null) {
            log.error("[fillContinuousData] Unknown date type: {}, returning original data", dateType);
            throw new IllegalArgumentException("fillContinuousData: Unknown date type: " + dateType);
        }

        // Get normalized date range based on date type
        LocalDateTime startDate = normalizeDateByType(entities.getFirst().getMetricDate(), dateType);
        LocalDateTime endDate = normalizeDateByType(entities.getLast().getMetricDate(), dateType);

        // Create date to entity mapping - use normalized date as key, keep first entity if duplicates
        Map<LocalDateTime, StoreProfilingStoreContinuousMetricsEntity> dateToEntityMap = entities.stream()
            .collect(Collectors.toMap(
                entity -> normalizeDateByType(entity.getMetricDate(), dateType),
                entity -> entity,
                (existing, replacement) -> existing // Keep first entity if duplicates
            ));

        // Generate consecutive dates and fill data
        List<StoreProfilingStoreContinuousMetricsEntity> result = new ArrayList<>();
        LocalDateTime currentDate = startDate;
        while (!currentDate.isAfter(endDate)) { 
            StoreProfilingStoreContinuousMetricsEntity existingEntity = dateToEntityMap.get(currentDate);
            
            if (existingEntity != null) {
                // Update the entity's date to the normalized date
                existingEntity.setMetricDate(currentDate);
                result.add(existingEntity);
            } else {
                // Create filled entity, use 0 value
                StoreProfilingStoreContinuousMetricsEntity filledEntity = createFilledEntity(
                    entities.get(0), currentDate);
                result.add(filledEntity);
            }
            
            currentDate = currentDate.plus(1, timeUnit);
        }

        return result;
    }

    /**
     * Normalize date based on date type
     * For year: yyyy-01-01 00:00:00
     * For month: yyyy-MM-01 00:00:00  
     * For day: yyyy-MM-dd 00:00:00
     */
    private LocalDateTime normalizeDateByType(LocalDateTime date, String dateType) {
        return switch (dateType.toLowerCase()) {
            case "year" -> LocalDateTime.of(date.getYear(), 1, 1, 0, 0, 0);
            case "month" -> LocalDateTime.of(date.getYear(), date.getMonth(), 1, 0, 0, 0);
            case "day" -> LocalDateTime.of(date.getYear(), date.getMonth(), date.getDayOfMonth(), 0, 0, 0);
            case "week" -> {
                // For week, we need to find the start of the week (Monday)
                LocalDate weekStart = date.toLocalDate().with(java.time.DayOfWeek.MONDAY);
                yield LocalDateTime.of(weekStart, java.time.LocalTime.of(0, 0, 0));
            }
            default -> date.toLocalDate().atStartOfDay();
        };
    }

    /**
     * Get the corresponding time unit based on date type
     */
    private ChronoUnit getChronoUnit(String dateType) {
        return switch (dateType.toLowerCase()) {
            case "day" -> ChronoUnit.DAYS;
            case "week" -> ChronoUnit.WEEKS;
            case "month" -> ChronoUnit.MONTHS;
            case "year" -> ChronoUnit.YEARS;
            default -> null;
        };
    }

    /**
     * Create filled entity
     */
    private StoreProfilingStoreContinuousMetricsEntity createFilledEntity(
            StoreProfilingStoreContinuousMetricsEntity template, LocalDateTime date) {
        StoreProfilingStoreContinuousMetricsEntity filledEntity = new StoreProfilingStoreContinuousMetricsEntity();
        filledEntity.setStoreId(template.getStoreId());
        filledEntity.setMetricCategory(template.getMetricCategory());
        filledEntity.setMetricName(template.getMetricName());
        filledEntity.setMetricDesc(template.getMetricDesc());
        filledEntity.setMetricValue(BigDecimal.ZERO); // Fill data with 0 value
        filledEntity.setMetricDate(date);
        filledEntity.setMetricDateType(template.getMetricDateType());
        return filledEntity;
    }

    @Override
    public StoreProfilingStoreInfoExtDto getStoreInfoByStoreId(String storeId) {

        if (!StringUtils.hasText(storeId)) {
            log.warn("[getStoreInfoByStoreId] storeId is empty or null");
            throw new IllegalArgumentException("storeId is empty or null");
        }

        // get store info basic info
        StoreProfilingStoreInfoDto storeInfo = getStoreProfilingStoreInfoByStoreId(storeId);
        if (storeInfo == null) {
            log.warn("[getStoreInfoByStoreId] Store info not found for storeId: {}", storeId);
            throw new IllegalArgumentException("Store info not found for storeId: " + storeId);
        }

        // get discrete metrics
        List<StoreProfilingStoreMetricsEntity> discreteMetrics = storeProfilingStoreMetricsRepository.findByStoreId(storeId);
        List<StoreProfilingStoreMetricsDto> discreteMetricsDto = discreteMetrics.stream().map(storeProfilingStoreMetricsDtoMapper::toDto).toList();

        // pass storeInfo to StoreProfilingStoreInfoExtDto
        StoreProfilingStoreInfoExtDto extDto = new StoreProfilingStoreInfoExtDto();

        extDto.setBasicInfo(storeInfo);

        if (CollectionUtils.isNotEmpty(discreteMetrics)) {

            // set store radar metrics
            List<StoreProfilingStoreMetricsDto> storeRadar = buildStoreRadar(discreteMetricsDto);
            extDto.setStoreRadarList(storeRadar);

            // set basic info from discrete metrics
            // metric_category = 'Basic' and metric_name = 'Store Type'
            String storeType = discreteMetricsDto.stream()
                .filter(metric -> "Basic".equals(metric.getMetricCategory()) && "Store Type".equals(metric.getMetricName()))
                .map(StoreProfilingStoreMetricsDto::getMetricValue)
                .findFirst()
                .orElse(null);
            extDto.setStoreType(storeType);

            // set store type from discrete metrics
            // metric_category = 'Basic' and metric_name = 'Store Source'
            List<String> storeSourceList = discreteMetricsDto.stream()
                .filter(metric -> "Basic".equals(metric.getMetricCategory()) && "Store Source".equals(metric.getMetricName()))
                .map(StoreProfilingStoreMetricsDto::getMetricValue)
                .collect(Collectors.toList());
            // adjust the order, mercaso store is the first one, remove duplicate store source, if mercaso store exists, remove mercaso store and add it to the first position
            if (storeSourceList.contains(MERCASO_STORE_SOURCE)) {
                storeSourceList.remove(MERCASO_STORE_SOURCE);
                storeSourceList.addFirst(MERCASO_STORE_SOURCE);
            }
            extDto.setStoreSource(String.join(" , ", storeSourceList));

            // metric_category = 'Basic' and metric_name = 'Store Open Hours'
            String storeOpenHours = discreteMetricsDto.stream()
                .filter(metric -> "Basic".equals(metric.getMetricCategory()) && "Store Open Hours".equals(metric.getMetricName()))
                .map(StoreProfilingStoreMetricsDto::getMetricValue)
                .findFirst()
                .orElse(null);
            extDto.setStoreOpenHours(storeOpenHours);

            // set grade from discrete metrics
            // metric_category = 'PO' and metric_name = 'Grade'
            String grade = discreteMetricsDto.stream()
                .filter(metric -> "PO".equals(metric.getMetricCategory()) && "Grade".equals(metric.getMetricName()))
                .map(StoreProfilingStoreMetricsDto::getMetricValue)
                .findFirst()
                .orElse(null);
            extDto.setStoreGrade(grade);

            // set alert tags from discrete metrics
            // metric_category = 'Alert'
            List<StoreProfilingStoreMetricsDto> alertValues = discreteMetricsDto.stream().filter(metric -> "Alert".equals(metric.getMetricCategory()))
                .collect(Collectors.toList());

            extDto.setStoreAlertTags(alertValues);

            // set around metrics
            StoreProfilingStoreAroundInfoDto storeAround = buildStoreAround(discreteMetricsDto);
            extDto.setStoreAround(storeAround);
        }

        return extDto;
    }

    private List<StoreProfilingStoreMetricsDto> buildStoreRadar(List<StoreProfilingStoreMetricsDto> discreteMetricsDto) {

        // poFrequencyScore
        StoreProfilingStoreMetricsDto frequencyScore = discreteMetricsDto.stream()
            .filter(metric -> "PO".equals(metric.getMetricCategory()) && "PO Frequency Score".equals(metric.getMetricName()))
            .findFirst()
            .orElse(null);
        // poDateCountScore
        StoreProfilingStoreMetricsDto dateCountScore = discreteMetricsDto.stream()
            .filter(metric -> "PO".equals(metric.getMetricCategory()) && "PO Date Count Score".equals(metric.getMetricName()))
            .findFirst()
            .orElse(null);
        // poAmountScore
        StoreProfilingStoreMetricsDto amountScore = discreteMetricsDto.stream()
            .filter(metric -> "PO".equals(metric.getMetricCategory()) && "PO Amount Score".equals(metric.getMetricName()))  
            .findFirst()
            .orElse(null);
        // poAovScore
        StoreProfilingStoreMetricsDto aovScore = discreteMetricsDto.stream()
            .filter(metric -> "PO".equals(metric.getMetricCategory()) && "PO AOV Score".equals(metric.getMetricName()))
            .findFirst()
            .orElse(null);
        // poDiscountScore
        StoreProfilingStoreMetricsDto discountScore = discreteMetricsDto.stream()
            .filter(metric -> "PO".equals(metric.getMetricCategory()) && "PO Discount Score".equals(metric.getMetricName()))
            .findFirst()
            .orElse(null);  

        List<StoreProfilingStoreMetricsDto> storeRadarList = Stream.of(frequencyScore, dateCountScore, amountScore, aovScore, discountScore)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        return storeRadarList;
    }

    
    private StoreProfilingStoreAroundInfoDto buildStoreAround(List<StoreProfilingStoreMetricsDto> discreteMetricsDto) {
        
        StoreProfilingStoreAroundInfoDto storeAround = new StoreProfilingStoreAroundInfoDto();
        //filter the metrics by metric_category = 'Basic' and metric_name in AROUND_GRADE_METRIC_LIST
        List<StoreProfilingStoreMetricsDto> grades = discreteMetricsDto.stream()
            .filter(metric -> "Basic".equals(metric.getMetricCategory()) && AROUND_GRADE_METRIC_LIST.contains(metric.getMetricName()))
            .collect(Collectors.toList());
        
        //filter the metrics by metric_category = 'Basic' and metric_name in AROUND_REAL_ESTATE_METRIC_LIST
        List<StoreProfilingStoreMetricsDto> realEstateStats = discreteMetricsDto.stream()
            .filter(metric -> "Basic".equals(metric.getMetricCategory()) && AROUND_REAL_ESTATE_METRIC_LIST.contains(metric.getMetricName()))
            .collect(Collectors.toList());
        
        //filter the metrics by metric_category = 'Basic' and metric_name in AROUND_RESIDENT_DEMOGRAPHICS_METRIC_LIST
        List<StoreProfilingStoreMetricsDto> residentDemographicsStats = discreteMetricsDto.stream()
            .filter(metric -> "Basic".equals(metric.getMetricCategory()) && AROUND_RESIDENT_DEMOGRAPHICS_METRIC_LIST.contains(metric.getMetricName()))
            .collect(Collectors.toList());

        grades.sort(Comparator.comparing(StoreProfilingStoreMetricsDto::getMetricName));
        realEstateStats.sort(Comparator.comparing(StoreProfilingStoreMetricsDto::getMetricName));
        residentDemographicsStats.sort(Comparator.comparing(StoreProfilingStoreMetricsDto::getMetricName));

        storeAround.setGrades(grades);
        storeAround.setRealEstateStats(realEstateStats);
        storeAround.setResidentDemographicsStats(residentDemographicsStats);

        return storeAround;
    }

    @Override
    public List<StoreProfilingDiscreteMetricsDto> findDiscreteMetricsListByStoreIdAndCategory(String storeId, DiscreteMetricsCategoryEnums category) {
        
        if (!StringUtils.hasText(storeId) || category == null) {
            log.warn("[findDiscreteMetricsListByStoreIdAndCategory] storeId or category is empty or null. storeId: {}, category: {}", storeId, category);
            return List.of();
        }

        List<DiscreteMetricsCategoryEnums.MetricCategoryName> metricCategoryNameList = category.getMetricCategoryNameList();
        
        // Extract unique categories for batch query
        List<String> categories = metricCategoryNameList.stream()
            .map(DiscreteMetricsCategoryEnums.MetricCategoryName::getCategory)
            .distinct()
            .collect(Collectors.toList());
        
        // Batch query by categories
        List<StoreProfilingStoreMetricsEntity> allEntities = 
            storeProfilingStoreMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, categories);
        
        // Filter by specific category+metricName combinations in memory
        List<StoreProfilingStoreMetricsEntity> entities = allEntities.stream()
            .filter(entity -> metricCategoryNameList.stream()
                .anyMatch(mcn -> mcn.getCategory().equals(entity.getMetricCategory()) 
                    && mcn.getMetricName().equals(entity.getMetricName())))
            .collect(Collectors.toList());
        
        log.info("[findDiscreteMetricsListByStoreIdAndCategory] Found {} entities for storeId: {} and category: {}", 
            entities.size(), storeId, category.name());

        if (entities.isEmpty()) {
            return List.of();
        }

        // Group by description
        Map<String, List<StoreProfilingStoreMetricsEntity>> groupedByDescription = new HashMap<>();
        
        for (StoreProfilingStoreMetricsEntity entity : entities) {
            // Find the corresponding MetricCategoryName to get description
            Optional<DiscreteMetricsCategoryEnums.MetricCategoryName> matchingConfig = metricCategoryNameList.stream()
                .filter(mcn -> mcn.getCategory().equals(entity.getMetricCategory()) 
                    && mcn.getMetricName().equals(entity.getMetricName()))
                .findFirst();
                
            if (matchingConfig.isPresent()) {
                String description = matchingConfig.get().getDescription();
                groupedByDescription.computeIfAbsent(description, k -> new ArrayList<>()).add(entity);
            }
        }
        
        // Convert grouped data to DTO
        List<StoreProfilingDiscreteMetricsDto> result = new ArrayList<>();
        
        for (Map.Entry<String, List<StoreProfilingStoreMetricsEntity>> entry : groupedByDescription.entrySet()) {

            // Create a map to store values by tag
            Map<String, String> valuesByTag = new HashMap<>();

            String description = entry.getKey();
            valuesByTag.put("title",description);
            List<StoreProfilingStoreMetricsEntity> groupEntities = entry.getValue();
            
            for (StoreProfilingStoreMetricsEntity entity : groupEntities) {
                // Find the tag for this entity
                Optional<DiscreteMetricsCategoryEnums.MetricCategoryName> config = metricCategoryNameList.stream()
                    .filter(mcn -> mcn.getCategory().equals(entity.getMetricCategory()) 
                        && mcn.getMetricName().equals(entity.getMetricName()))
                    .findFirst();
                    
                if (config.isPresent()) {
                    String tag = config.get().getTag();
                    valuesByTag.put(tag, entity.getMetricValue());
                    if ("value".equals(tag)) {
                        valuesByTag.put("description", entity.getMetricDesc());
                    }
                }
            }
            
            // Create DTO based on tag mapping
            StoreProfilingDiscreteMetricsDto dto = new StoreProfilingDiscreteMetricsDto(
                valuesByTag.getOrDefault("title",""),
                valuesByTag.getOrDefault("name", ""), // metricsName
                valuesByTag.getOrDefault("value", ""), // metricsValue
                valuesByTag.getOrDefault("description", ""), // metricsDescription
                processSkuImageUrl(valuesByTag.getOrDefault("sku", ""))   // linkUrl
            );
            
            result.add(dto);
        }
        
        // Sort by metricsName
        result.sort(Comparator.comparing(StoreProfilingDiscreteMetricsDto::getMetricsName));

        log.info("[findDiscreteMetricsListByStoreIdAndCategory] Returning {} discrete metrics for storeId: {} and category: {}", 
            result.size(), storeId, category);

        return result;
    }

    private String processSkuImageUrl(String sku) {

        if (!StringUtils.hasText(sku)) {
            return "";
        }

        List<ItemCategoryDto> items = itemManagementServiceClientAdaptor.findItemsBySkuIn(List.of(sku));

        return items.stream()
            .findFirst()
            .map(ItemCategoryDto::getPhotoUrl)
            .filter(StringUtils::hasText)
            .orElse("");
    }

    @Override
    public StoreProfilingStoreSummaryDto getAllStoreList() {

        log.info("[getAllStoreList] Starting to fetch all store list with metrics");
        List<StoreProfilingStoreInfoEntity> storeEntities = 
            storeProfilingStoreInfoRepository.findByIsMercasoStoreOrIsGoogleStore(true, true);

        log.info("[getAllStoreList] Found {} stores matching criteria", storeEntities.size());

        List<StoreProfilingStoreInfoDto> storeList = storeEntities.stream()
            .map(storeProfilingStoreInfoDtoMapper::toDto)
            .collect(Collectors.toList());

        List<String> operationAreaZipCodeList = OperationAreaZipCodeConstant.OPERATION_AREA_ZIP_CODE_LIST;

        return new StoreProfilingStoreSummaryDto(storeList, operationAreaZipCodeList);
    }
}
