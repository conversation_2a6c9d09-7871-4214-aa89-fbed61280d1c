package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.exception.ErrorCodeEnums;
import com.mercaso.data.master_catalog.exception.MasterCatalogBusinessException;
import com.mercaso.data.master_catalog.mapper.MasterCatalogTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import com.mercaso.data.utils.BusinessNumberGenerator;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MasterCatalogTaskServiceImpl implements MasterCatalogTaskService {

  private static final String ADMIN_ROLE = "admin_master_catalog";
  private static final String REVIEWER_ROLE = "admin_master_catalog_reviewer";

  private final MasterCatalogTaskMapper masterCatalogTaskMapper;

  private final MasterCatalogTaskRepository masterCatalogTaskRepository;
  private final MasterCatalogPotentiallyDuplicateRawDataRepository duplicateRawDataRepository;

  @Override
  public List<MasterCatalogTaskDto> createTasks(UUID jobId, MasterCatalogTaskType type, Integer taskCount) {
    if (taskCount < 1) {
      return List.of();
    }
    List<MasterCatalogTask> tasks = new ArrayList<>();
    for (int i = 0; i < taskCount; i++) {
      MasterCatalogTask task = MasterCatalogTask.builder()
          .jobId(jobId)
          .taskNumber(BusinessNumberGenerator.generateTaskNumber())
          .status(MasterCatalogTaskStatus.PENDING)
          .type(type)
          .build();
      tasks.add(task);
    }

    return masterCatalogTaskRepository.saveAll(tasks).stream().map(masterCatalogTaskMapper::toDto)
        .collect(Collectors.toList());
  }

  @Override
  public Page<MasterCatalogTaskDto> search(String jobId, String taskNumber, String assignTo, MasterCatalogTaskStatus status, MasterCatalogTaskType type,
      PageRequest pageRequest) {
    List<String> userRoles = SecurityContextUtil.getMercasoRoles();
    if (userRoles == null || (!userRoles.contains(ADMIN_ROLE) && !userRoles.contains(REVIEWER_ROLE))) {
      return Page.empty();
    }
    String reviewerUserId = getReviewerUserId(assignTo);
    Specification<MasterCatalogTask> specification = (root, query, builder) -> {
      return builder.and(
          Stream.of(
                  StringUtils.isNotBlank(jobId) ?
                      builder.equal(root.get("jobId"), UUID.fromString(jobId)) : null,
                  addIfNotNull(builder, root, "taskNumber", taskNumber),
                  addIfNotNull(builder, root, "assignedTo", reviewerUserId),
                  addIfNotNull(builder, root, "status", status),
                  addIfNotNull(builder, root, "type", type)
              )
              .filter(Objects::nonNull)
              .toArray(Predicate[]::new));
    };
    Page<MasterCatalogTask> taskPage = masterCatalogTaskRepository.findAll(specification, pageRequest);
    List<UUID> taskIds = taskPage.getContent().stream().map(MasterCatalogTask::getId).toList();
    List<MasterCatalogPotentiallyDuplicateRawData> duplicateRawDatas = duplicateRawDataRepository.findByTaskIdIn(taskIds);
    Map<UUID, List<MasterCatalogPotentiallyDuplicateRawData>> duplicationRawDataMap = duplicateRawDatas.stream()
        .collect(Collectors.groupingBy(MasterCatalogPotentiallyDuplicateRawData::getTaskId));
    return taskPage.map(task -> calculateTaskCount(task, duplicationRawDataMap));
  }

  @Override
  public MasterCatalogTaskDto getDetailsById(UUID id) {
    MasterCatalogTask task = masterCatalogTaskRepository.findById(id).orElseThrow(
        () -> new MasterCatalogBusinessException(ErrorCodeEnums.MASTER_CATALOG_RAW_DATA_NOT_FOUND,
            "Task not found with id: " + id));

    Map<UUID, List<MasterCatalogPotentiallyDuplicateRawData>> duplicationRawDataMap = duplicateRawDataRepository.findByTaskId(
            task.getId()).stream().collect(Collectors.groupingBy(MasterCatalogPotentiallyDuplicateRawData::getTaskId));
    return calculateTaskCount(task, duplicationRawDataMap);
  }

  private MasterCatalogTaskDto calculateTaskCount(MasterCatalogTask task, Map<UUID, List<MasterCatalogPotentiallyDuplicateRawData>> duplicationRawDataMap) {
    MasterCatalogTaskDto taskDto = masterCatalogTaskMapper.toDto(task);
    List<MasterCatalogPotentiallyDuplicateRawData> duplicateRawDataList = duplicationRawDataMap.getOrDefault(taskDto.getId(),
        List.of());
    Map<PotentiallyDuplicateRawDataStatus, Long> counts = duplicateRawDataList.stream()
        .collect(Collectors.groupingBy(MasterCatalogPotentiallyDuplicateRawData::getStatus, Collectors.counting()));

    taskDto.setTotalRecordCount(duplicateRawDataList.size());
    taskDto.setPendingRecordCount(counts.getOrDefault(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW, 0L).intValue());
    taskDto.setInStageRecordCount(counts.getOrDefault(PotentiallyDuplicateRawDataStatus.IN_STAGE, 0L).intValue());
    taskDto.setSubmittedRecordCount(counts.getOrDefault(PotentiallyDuplicateRawDataStatus.SECOND_ROUND_REVIEWED,
        counts.getOrDefault(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED, 0L)).intValue());
    return taskDto;
  }

  @Override
  public void assign(UUID id, String assignTo) {
    MasterCatalogTask task = masterCatalogTaskRepository.findById(id).orElseThrow(
        () -> new IllegalArgumentException("task not fount with id:" + id));
    validateTask(task);

    task.setAssignedBy(SecurityContextUtil.getLoginUserId());
    task.setAssignedTo(assignTo);
    task.setStatus(MasterCatalogTaskStatus.ASSIGNED);
    masterCatalogTaskRepository.save(task);
    log.info("Task with id {} has been assigned to {}", id, assignTo);
  }

  private <T> Predicate addIfNotNull(CriteriaBuilder builder, Root<MasterCatalogTask> root,
      String field, T value) {
    if (value == null) {
      return null;
    }
    if (value instanceof String && StringUtils.isBlank((String) value)) {
      return null;
    }
    return builder.equal(root.get(field), value);
  }

  private String getReviewerUserId(String assignTo){
    List<String> roles = SecurityContextUtil.getMercasoRoles();
    if (!roles.contains(ADMIN_ROLE) && roles.contains(REVIEWER_ROLE)) {
      return SecurityContextUtil.getLoginUserId();
    } else if (StringUtils.isNotBlank(assignTo)) {
      return assignTo;
    }
    return null;
  }

  private void validateTask(MasterCatalogTask task) {
    if (MasterCatalogTaskStatus.PENDING != task.getStatus()
        && MasterCatalogTaskStatus.ASSIGNED != task.getStatus()) {
      log.warn("The task with id {} is not in available status, cannot update.", task.getId());
      throw new IllegalArgumentException("the task is not a available task");
    }
  }
}
