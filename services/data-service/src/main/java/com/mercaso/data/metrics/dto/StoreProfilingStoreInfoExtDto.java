package com.mercaso.data.metrics.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
public class StoreProfilingStoreInfoExtDto extends StoreProfilingStoreInfoDto{

    private String storeGrade;
    private List<StoreProfilingStoreMetricsDto> storeRadarList;
    private List<StoreProfilingStoreMetricsDto> storeAlertTags;
    private StoreProfilingStoreAroundInfoDto storeAround;
    
    public void setBasicInfo(StoreProfilingStoreInfoDto storeInfo) {
        this.setStoreId(storeInfo.getStoreId());
        this.setStoreName(storeInfo.getStoreName());
        this.setAddressName(storeInfo.getAddressName());
        this.setLatitude(storeInfo.getLatitude());
        this.setLongitude(storeInfo.getLongitude());
        this.setZipCode(storeInfo.getZipCode());
        this.setIsNrsStore(storeInfo.getIsNrsStore());
        this.setIsSquareStore(storeInfo.getIsSquareStore());
        this.setIsGoogleStore(storeInfo.getIsGoogleStore());
        this.setIs7ElevenStore(storeInfo.getIs7ElevenStore());
        this.setIsMercasoStore(storeInfo.getIsMercasoStore());
    }
}
