package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.DEFAULT_BATCH_SIZE;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobListDto;
import com.mercaso.data.master_catalog.service.MasterCatalogBatchJobService;
import jakarta.validation.constraints.Min;
import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import java.time.Instant;
import java.util.Date;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/batch-jobs")
@RequiredArgsConstructor
@Validated
public class MasterCatalogBatchJobController {

    private final MasterCatalogBatchJobService batchJobService;

    @PreAuthorize("hasAnyAuthority('master-catalog:write:batch-jobs')")
    @PostMapping("/start")
    public ResponseEntity<Void> startProcessing(
        @RequestParam(value = "batchSize", defaultValue = DEFAULT_BATCH_SIZE) Integer batchSize) {
        batchJobService.startInitialProcessing(batchSize);
        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:read:batch-jobs')")
    @GetMapping("/{id}")
    public MasterCatalogBatchJobDto searchById(@PathVariable("id") UUID jobId) {
        return batchJobService.searchDetailById(jobId);
    }

    @PreAuthorize("hasAnyAuthority('master-catalog:read:batch-jobs')")
    @GetMapping("/search")
    public CustomPage<MasterCatalogBatchJobListDto> search(
        @RequestParam(value = "jobNumber", required = false) String jobNumber,
        @RequestParam(value = "createdStartDate", required = false) Instant createdStartDate,
        @RequestParam(value = "createdEndDate", required = false) Instant createdEndDate,
        @RequestParam(value = "completedStartDate", required = false) Instant completedStartDate,
        @RequestParam(value = "completedEndDate", required = false) Instant completedEndDate,
        @Min(1) @RequestParam(value = "page") Integer page,
        @Min(1) @RequestParam(value = "pageSize") Integer pageSize) {
        PageRequest pageRequest = PageRequest.of(page - 1, pageSize, Sort.by(Order.desc("createdAt")));
        Page<MasterCatalogBatchJobListDto> masterCatalogBatchJobList = batchJobService.search(jobNumber,
            createdStartDate, createdEndDate, completedStartDate, completedEndDate, pageRequest);
        return new CustomPage<MasterCatalogBatchJobListDto>().build(masterCatalogBatchJobList);
    }
}
