package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import jakarta.validation.constraints.Min;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/tasks")
@RequiredArgsConstructor
@Validated
public class MasterCatalogTaskResource {

  private final MasterCatalogTaskService masterCatalogTaskService;

  @PreAuthorize("hasAnyAuthority('master-catalog:read:tasks')")
  @GetMapping("/search")
  public CustomPage<MasterCatalogTaskDto> search(
      @RequestParam(value = "jobId", required = false) String jobId,
      @RequestParam(value = "taskNumber", required = false) String taskNumber,
      @RequestParam(value = "assignTo", required = false) String assignTo,
      @RequestParam(value = "status", required = false) MasterCatalogTaskStatus status,
      @RequestParam(value = "type", required = false) MasterCatalogTaskType type,
      @Min(1) @RequestParam(value = "page") Integer page,
      @Min(1) @RequestParam(value = "pageSize") Integer pageSize) {
    PageRequest pageRequest = PageRequest.of(page - 1, pageSize, Sort.by(Sort.Direction.DESC, "createdAt"));
    Page<MasterCatalogTaskDto> masterCatalogTaskList = masterCatalogTaskService.search(jobId, taskNumber, assignTo, status, type, pageRequest);
    return new CustomPage<MasterCatalogTaskDto>().build(masterCatalogTaskList);
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:read:tasks')")
  @GetMapping("/{id}")
  public MasterCatalogTaskDto getById(
      @PathVariable("id") UUID id) {
    return masterCatalogTaskService.getDetailsById(id);
  }

  @PreAuthorize("hasAnyAuthority('master-catalog:write:tasks')")
  @PutMapping("/assign/{id}")
  public void assign(
      @PathVariable("id") UUID id,
      @RequestParam("assignTo") String assignTo) {
    masterCatalogTaskService.assign(id, assignTo);
  }
}
