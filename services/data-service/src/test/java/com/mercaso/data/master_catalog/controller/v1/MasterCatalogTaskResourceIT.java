package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogTaskResourceApiUtils;
import com.mercaso.data.utils.BusinessNumberGenerator;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class MasterCatalogTaskResourceIT extends AbstractIT {

  @Autowired
  private MasterCatalogTaskResourceApiUtils masterCatalogTaskResourceApiUtils;
  @Autowired
  private MasterCatalogTaskRepository masterCatalogTaskRepository;
  @Autowired
  private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;

  @Test
  void assign() {
    UUID jobId = UUID.randomUUID();
    String taskId = UUID.randomUUID().toString();
    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(UUID.fromString(taskId))
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    MasterCatalogBatchJob job = MasterCatalogBatchJob.builder().id(jobId)
        .jobNumber(UUID.randomUUID().toString())
        .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS).build();

    masterCatalogTaskRepository.save(task);
    masterCatalogBatchJobRepository.save(job);

    masterCatalogTaskResourceApiUtils.assign(taskId, "zhangsan");

    MasterCatalogTask masterCatalogTask = masterCatalogTaskRepository.findById(UUID.fromString(taskId)).orElseThrow();

    assert masterCatalogTask.getId().equals(UUID.fromString(taskId));
    assert masterCatalogTask.getStatus().equals(MasterCatalogTaskStatus.ASSIGNED);
    assert masterCatalogTask.getAssignedTo().equals("zhangsan");
  }

  @Test
  void search() {
    UUID jobId = UUID.randomUUID();
    String taskNumber = UUID.randomUUID().toString();
    MasterCatalogTask pendingTask = MasterCatalogTask.builder()
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(taskNumber)
        .status(MasterCatalogTaskStatus.PENDING)
        .build();
    
    MasterCatalogTask assignedTask = MasterCatalogTask.builder()
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.ASSIGNED)
        .assignedTo("testUser")
        .build();

    masterCatalogTaskRepository.save(pendingTask);
    masterCatalogTaskRepository.save(assignedTask);

    var result = masterCatalogTaskResourceApiUtils.search(null, null, null, MasterCatalogTaskStatus.PENDING, null, 1, 10);

    assert result.getData().stream().allMatch(task -> task.getStatus() == MasterCatalogTaskStatus.PENDING);
    assert result.getData().stream().allMatch(task -> task.getTaskNumber().equals(taskNumber));
  }

  @Test
  void getDetailById() {
    UUID jobId = UUID.randomUUID();
    UUID taskId = UUID.randomUUID();
    
    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(taskId)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .taskNumber(BusinessNumberGenerator.generateTaskNumber())
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    masterCatalogTaskRepository.save(task);

    var result = masterCatalogTaskResourceApiUtils.getById(taskId);

    assert result.getId().equals(taskId);
    assert result.getStatus() == MasterCatalogTaskStatus.PENDING;
    assert result.getTotalRecordCount() == 0;
  }
}
