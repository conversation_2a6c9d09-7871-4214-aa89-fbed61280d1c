package com.mercaso.data.metrics.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.mercaso.data.dto.CustomPageable;
import com.mercaso.data.metrics.adapter.ItemManagementServiceClientAdaptor;
import com.mercaso.data.metrics.dto.SearchStoreAddressDto;
import com.mercaso.data.metrics.dto.StoreAddressFilter;
import com.mercaso.data.metrics.dto.StoreProfilingDiscreteMetricsDto;
import com.mercaso.data.metrics.dto.StoreProfilingDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreContinuousMetricsDto;
import com.mercaso.data.metrics.dto.StoreProfilingContinuousMetricsListDto;
import com.mercaso.data.metrics.dto.StoreProfilingStoreMetricsDto;
import com.mercaso.data.metrics.enums.ContinuousMetricsCategoryEnums;
import com.mercaso.data.metrics.enums.DiscreteMetricsCategoryEnums;
import com.mercaso.data.metrics.entity.StoreProfilingStoreContinuousMetricsEntity;
import com.mercaso.data.metrics.entity.StoreProfilingStoreInfoEntity;
import com.mercaso.data.metrics.entity.StoreProfilingStoreMetricsEntity;
import com.mercaso.data.metrics.entity.StoreProfilingTagEntity;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreContinuousMetricsDtoMapper;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreInfoDtoMapper;
import com.mercaso.data.metrics.mapper.StoreProfilingStoreMetricsDtoMapper;
import com.mercaso.data.metrics.mock.MetricsStoreProfilingStoreInfoEntityMock;
import com.mercaso.data.metrics.repository.StoreProfilingStoreContinuousMetricsRepository;
import com.mercaso.data.metrics.repository.StoreProfilingStoreInfoRepository;
import com.mercaso.data.metrics.repository.StoreProfilingStoreMetricsRepository;
import com.mercaso.data.metrics.repository.StoreProfilingTagRepository;
import com.mercaso.data.metrics.service.impl.StoreProfilingServiceImpl;
import com.mercaso.ims.client.dto.ItemCategoryDto;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoDto;
import org.springframework.data.jpa.domain.Specification;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.*;
import java.util.stream.Collectors;
import com.mercaso.data.metrics.dto.StoreProfilingStoreInfoExtDto;

@ExtendWith(MockitoExtension.class)
class StoreProfilingServiceTest {

    private final StoreProfilingStoreMetricsRepository storeProfilingStoreMetricsRepository = Mockito.mock(
        StoreProfilingStoreMetricsRepository.class);

    private final StoreProfilingStoreContinuousMetricsRepository storeProfilingStoreContinuousMetricsRepository = Mockito.mock(
        StoreProfilingStoreContinuousMetricsRepository.class);

    private final StoreProfilingStoreInfoRepository storeProfilingStoreInfoRepository = Mockito.mock(
        StoreProfilingStoreInfoRepository.class);

    private final StoreProfilingTagRepository storeProfilingTagRepository = Mockito.mock(
        StoreProfilingTagRepository.class
    );

    private final StoreProfilingStoreMetricsDtoMapper storeProfilingStoreMetricsDtoMapper = Mockito.mock(
        StoreProfilingStoreMetricsDtoMapper.class);

    private final StoreProfilingStoreInfoDtoMapper storeProfilingStoreInfoDtoMapper = Mockito.mock(
        StoreProfilingStoreInfoDtoMapper.class);

    private final StoreProfilingStoreContinuousMetricsDtoMapper storeProfilingStoreContinuousMetricsDtoMapper = Mockito.mock(
        StoreProfilingStoreContinuousMetricsDtoMapper.class);

    private final ItemManagementServiceClientAdaptor itemManagementServiceClientAdaptor = Mockito.mock(
        ItemManagementServiceClientAdaptor.class);

    private final StoreProfilingService storeProfilingService = new StoreProfilingServiceImpl(
        storeProfilingStoreMetricsRepository,
        storeProfilingStoreInfoRepository, 
        storeProfilingStoreContinuousMetricsRepository, 
        storeProfilingTagRepository, 
        storeProfilingStoreMetricsDtoMapper,
        storeProfilingStoreInfoDtoMapper, 
        storeProfilingStoreContinuousMetricsDtoMapper,
        itemManagementServiceClientAdaptor
    );

    private StoreProfilingStoreContinuousMetricsEntity entity1;
    private StoreProfilingStoreContinuousMetricsEntity entity2;
    private StoreProfilingStoreContinuousMetricsDto dto1;
    private StoreProfilingStoreContinuousMetricsDto dto2;

    @BeforeEach
    void setUp() {
        entity1 = createTestEntity("store-1", "metric-1");
        entity2 = createTestEntity("store-1", "metric-2");
        dto1 = createTestDto("store-1", "metric-1");
        dto2 = createTestDto("store-1", "metric-2");
    }

    private StoreProfilingStoreContinuousMetricsEntity createTestEntity(String storeId, String metricName) {
        StoreProfilingStoreContinuousMetricsEntity entity = new StoreProfilingStoreContinuousMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricCategory("test-category");
        entity.setMetricName(metricName);
        entity.setMetricDesc("test description");
        entity.setMetricValue(new BigDecimal("100.50"));
        entity.setMetricDate(LocalDateTime.now());
        entity.setMetricDateType("day");
        return entity;
    }

    private StoreProfilingStoreContinuousMetricsDto createTestDto(String storeId, String metricName) {
        StoreProfilingStoreContinuousMetricsDto dto = new StoreProfilingStoreContinuousMetricsDto();
        dto.setStoreId(storeId);
        dto.setMetricCategory("test-category");
        dto.setMetricName(metricName);
        dto.setMetricDesc("test description");
        dto.setMetricValue(new BigDecimal("100.50"));
        dto.setMetricDate(LocalDateTime.now());
        dto.setMetricDateType("day");
        return dto;
    }

    private StoreProfilingStoreContinuousMetricsEntity createTestEntityWithDate(String storeId, String metricName, String dateType, LocalDateTime date) {
        StoreProfilingStoreContinuousMetricsEntity entity = new StoreProfilingStoreContinuousMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricCategory("test-category");
        entity.setMetricName(metricName);
        entity.setMetricDesc("test description");
        entity.setMetricValue(new BigDecimal("100.50"));
        entity.setMetricDate(date);
        entity.setMetricDateType(dateType);
        return entity;
    }

    private StoreProfilingStoreContinuousMetricsDto createTestDtoWithDate(String storeId, String metricName, String dateType, LocalDateTime date) {
        return createTestDtoWithDate(storeId, metricName, dateType, date, new BigDecimal("100.50"));
    }

    private StoreProfilingStoreContinuousMetricsDto createTestDtoWithDate(String storeId, String metricName, String dateType, LocalDateTime date, BigDecimal value) {
        StoreProfilingStoreContinuousMetricsDto dto = new StoreProfilingStoreContinuousMetricsDto();
        dto.setStoreId(storeId);
        dto.setMetricCategory("test-category");
        dto.setMetricName(metricName);
        dto.setMetricDesc("test description");
        dto.setMetricValue(value);
        dto.setMetricDate(date);
        dto.setMetricDateType(dateType);
        return dto;
    }

    @Test
    void searchStoreAddressesV2_EmptyKeyword_ReturnsAllAddressesForStoreProfiling() {

        StoreProfilingStoreInfoEntity entity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword("");
        CustomPageable customPageable = new CustomPageable();
        customPageable.setPageNumber(0);
        customPageable.setPageSize(10);
        filter.setPageable(customPageable);
        Page<StoreProfilingStoreInfoEntity> mockPage = new PageImpl<>(List.of(entity));
        when(storeProfilingStoreInfoRepository.queryAll(any(Pageable.class))).thenReturn(mockPage);

        // When
        Page<SearchStoreAddressDto> result = storeProfilingService.searchStoreAddressesForStoreProfiling(filter);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1);
    }

    @Test
    void searchStoreAddressesV2_WithKeyword_ReturnsMatchingAddressesForStoreProfiling() {
        StoreProfilingStoreInfoEntity entity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setKeyword("test");
        CustomPageable customPageable = new CustomPageable();
        customPageable.setPageNumber(0);
        customPageable.setPageSize(10);
        filter.setPageable(customPageable);
        Page<StoreProfilingStoreInfoEntity> mockPage = new PageImpl<>(List.of(entity));
        when(storeProfilingStoreInfoRepository.findBySearchQuery(anyString(), any(Pageable.class))).thenReturn(mockPage);

        // When
        Page<SearchStoreAddressDto> result = storeProfilingService.searchStoreAddressesForStoreProfiling(filter);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotalElements()).isEqualTo(1);
    }

    @Test
    void findMetricsByStoreId_ReturnsListOfStoreProfilingStoreMetricsDtoForStoreProfiling() {
        StoreProfilingStoreMetricsEntity entity = new StoreProfilingStoreMetricsEntity();
        entity.setStoreId("123");
        when(storeProfilingStoreMetricsRepository.findByStoreId(anyString())).thenReturn(List.of(entity));

        List<StoreProfilingStoreMetricsDto> result = storeProfilingService.findMetricsByStoreId("123");
        assertThat(result).isNotNull();
    }


    @Test
    void getStoreProfilingByStoreId_ReturnsStoreProfilingDtoForStoreProfiling() {
        StoreProfilingStoreInfoEntity entity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        when(storeProfilingStoreInfoRepository.findById(anyString())).thenReturn(Optional.of(entity));

        List<StoreProfilingStoreMetricsEntity> metrics = new ArrayList<>();
        StoreProfilingStoreMetricsEntity metric = new StoreProfilingStoreMetricsEntity();
        metric.setStoreId("123");
        metrics.add(metric);
        when(storeProfilingStoreMetricsRepository.findByStoreId(anyString())).thenReturn(metrics);

        StoreProfilingDto result = storeProfilingService.getStoreProfilingByStoreId("123");
        assertThat(result).isNotNull();
    }

    @Test
    void searchStoreByTags_whenMatchAny_returnsFilteredStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("category-electronics,brand-samsung");
        filter.setMatch("any");
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        List<String> storeIdsFound = Arrays.asList("store1", "store2");
        Page<String> mockedStoreIdsPage = new PageImpl<>(storeIdsFound, PageRequest.of(0, 10), storeIdsFound.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class),
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);

        StoreProfilingStoreInfoEntity storeInfo1 = new StoreProfilingStoreInfoEntity();
        storeInfo1.setStoreId("store1");
        StoreProfilingStoreInfoEntity storeInfo2 = new StoreProfilingStoreInfoEntity();
        storeInfo2.setStoreId("store2");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfo1, storeInfo2);

        when(storeProfilingStoreInfoRepository.findAllById(storeIdsFound)).thenReturn(storeInfoEntities);

        StoreProfilingStoreInfoDto dto1 = new StoreProfilingStoreInfoDto();
        dto1.setStoreId("store1");
        StoreProfilingStoreInfoDto dto2 = new StoreProfilingStoreInfoDto();
        dto2.setStoreId("store2");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfo1)).thenReturn(dto1);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfo2)).thenReturn(dto2);

        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(storeIdsFound.size());
        assertThat(resultPage.getContent()).hasSize(storeIdsFound.size());
        assertThat(resultPage.getContent().get(0).getStoreId()).isEqualTo("store1");
        assertThat(resultPage.getContent().get(1).getStoreId()).isEqualTo("store2");
    }

    @Test
    void searchStoreByTags_whenMatchAll_returnsFilteredStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("feature-waterproof,color-black");
        filter.setMatch("all");
        CustomPageable customPageable = new CustomPageable(0, 5);
        filter.setPageable(customPageable);

        List<String> storeIdsFound = Arrays.asList("store3");
        Page<String> mockedStoreIdsPage = new PageImpl<>(storeIdsFound, PageRequest.of(0, 5), storeIdsFound.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class),
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);

        StoreProfilingStoreInfoEntity storeInfo3 = new StoreProfilingStoreInfoEntity();
        storeInfo3.setStoreId("store3");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfo3);

        when(storeProfilingStoreInfoRepository.findAllById(storeIdsFound)).thenReturn(storeInfoEntities);

        StoreProfilingStoreInfoDto dto3 = new StoreProfilingStoreInfoDto();
        dto3.setStoreId("store3");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfo3)).thenReturn(dto3);

        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(1);
        assertThat(resultPage.getContent()).hasSize(1);
        assertThat(resultPage.getContent().get(0).getStoreId()).isEqualTo("store3");
    }

    @Test
    void searchStoreByTags_whenMatchTypeNull_returnsAllStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setMatch(null);
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        List<String> allStoreIds = Arrays.asList("storeA", "storeB", "storeC");
        Page<String> mockedStoreIdsPage = new PageImpl<>(allStoreIds, PageRequest.of(0, 10), allStoreIds.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class), // Specification will be cb.conjunction()
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);

        StoreProfilingStoreInfoEntity storeInfoA = new StoreProfilingStoreInfoEntity(); storeInfoA.setStoreId("storeA");
        StoreProfilingStoreInfoEntity storeInfoB = new StoreProfilingStoreInfoEntity(); storeInfoB.setStoreId("storeB");
        StoreProfilingStoreInfoEntity storeInfoC = new StoreProfilingStoreInfoEntity(); storeInfoC.setStoreId("storeC");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfoA, storeInfoB, storeInfoC);

        when(storeProfilingStoreInfoRepository.findAllById(allStoreIds)).thenReturn(storeInfoEntities);

        // Mock mappers
        StoreProfilingStoreInfoDto dtoA = new StoreProfilingStoreInfoDto(); dtoA.setStoreId("storeA");
        StoreProfilingStoreInfoDto dtoB = new StoreProfilingStoreInfoDto(); dtoB.setStoreId("storeB");
        StoreProfilingStoreInfoDto dtoC = new StoreProfilingStoreInfoDto(); dtoC.setStoreId("storeC");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoA)).thenReturn(dtoA);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoB)).thenReturn(dtoB);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoC)).thenReturn(dtoC);


        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(allStoreIds.size());
        assertThat(resultPage.getContent().stream().map(StoreProfilingStoreInfoDto::getStoreId))
            .containsExactlyInAnyOrder("storeA", "storeB", "storeC");
    }

    @Test
    void searchStoreByTags_whenTagsAreNullOrEmpty_returnsAllStores() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags(null);
        filter.setMatch("any");
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        List<String> allStoreIds = Arrays.asList("storeX", "storeY");
        Page<String> mockedStoreIdsPage = new PageImpl<>(allStoreIds, PageRequest.of(0, 10), allStoreIds.size());

        when(storeProfilingStoreMetricsRepository.findDistinctStoreIds(
            any(Specification.class),
            any(Pageable.class)))
            .thenReturn(mockedStoreIdsPage);
        
        StoreProfilingStoreInfoEntity storeInfoX = new StoreProfilingStoreInfoEntity(); storeInfoX.setStoreId("storeX");
        StoreProfilingStoreInfoEntity storeInfoY = new StoreProfilingStoreInfoEntity(); storeInfoY.setStoreId("storeY");
        List<StoreProfilingStoreInfoEntity> storeInfoEntities = Arrays.asList(storeInfoX, storeInfoY);

        when(storeProfilingStoreInfoRepository.findAllById(allStoreIds)).thenReturn(storeInfoEntities);
        
        // Mock mappers
        StoreProfilingStoreInfoDto dtoX = new StoreProfilingStoreInfoDto(); dtoX.setStoreId("storeX");
        StoreProfilingStoreInfoDto dtoY = new StoreProfilingStoreInfoDto(); dtoY.setStoreId("storeY");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoX)).thenReturn(dtoX);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoY)).thenReturn(dtoY);

        // When
        Page<StoreProfilingStoreInfoDto> resultPage = storeProfilingService.searchStoreByTags(filter);

        // Then
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(allStoreIds.size());

        // Test with empty tags as well
        filter.setTags("");
        resultPage = storeProfilingService.searchStoreByTags(filter);
        assertThat(resultPage).isNotNull();
        assertThat(resultPage.getTotalElements()).isEqualTo(allStoreIds.size());
    }


    @Test
    void searchStoreByTags_whenTagFormatIsInvalid_throwsIllegalArgumentException() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("categoryelectronics");
        filter.setMatch("any");
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.searchStoreByTags(filter);
        });
        assertThat(exception.getMessage()).contains("Tag format error");
    }

    @Test
    void searchStoreByTags_whenMatchTypeIsInvalid_throwsIllegalArgumentException() {
        // Given
        StoreAddressFilter filter = new StoreAddressFilter();
        filter.setTags("brand-apple");
        filter.setMatch("sometimes"); // Invalid match type
        CustomPageable customPageable = new CustomPageable(0, 10);
        filter.setPageable(customPageable);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.searchStoreByTags(filter);
        });
        assertThat(exception.getMessage()).contains("Match value must be 'all' or 'any'");
    }

    @Test
    void getStoreTags_whenTags_returnsListOfTags() {
        when(storeProfilingTagRepository.findAll()).thenReturn(List.of(new StoreProfilingTagEntity(1L,"tag1"), new StoreProfilingTagEntity(2L,"tag2")));
        List<String> tags = storeProfilingService.getStoreTags();
        assertThat(tags).isNotEmpty();
        assertThat(tags).containsExactlyInAnyOrder("tag1", "tag2"); 
    }

    @Test
    void findContinuousMetricNamesByStoreId_ShouldReturnDistinctMetricNames() {
        // Given
        String storeId = "store-1";
        List<String> metricNames = Arrays.asList("metric-1", "metric-2");
        when(storeProfilingStoreContinuousMetricsRepository.findDistinctMetricNamesByStoreId(storeId)).thenReturn(metricNames);

        // When
        List<String> result = storeProfilingService.findContinuousMetricNamesByStoreId(storeId);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("metric-1"));
        assertTrue(result.contains("metric-2"));
    }

    @Test
    void findContinuousMetricNamesByStoreId_ShouldReturnEmptyList_WhenStoreIdIsEmpty() {
        // When
        List<String> result = storeProfilingService.findContinuousMetricNamesByStoreId("");

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findContinuousMetricNamesByStoreId_ShouldReturnEmptyList_WhenStoreIdIsNull() {
        // When
        List<String> result = storeProfilingService.findContinuousMetricNamesByStoreId(null);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldReturnDtoList() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(entity1);
        List<StoreProfilingStoreContinuousMetricsDto> dtos = Arrays.asList(dto1);
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(entities)).thenReturn(dtos);

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(storeId, result.get(0).getStoreId());
        assertEquals(metricName, result.get(0).getMetricName());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldReturnEmptyList_WhenStoreIdIsEmpty() {
        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName("", "metric-1");

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldReturnEmptyList_WhenMetricNameIsEmpty() {
        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName("store-1", "");

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldFillMissingDailyData() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        
        // Create test entities with gaps in daily data
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "day", LocalDateTime.of(2024, 1, 1, 12, 0)),
            createTestEntityWithDate(storeId, metricName, "day", LocalDateTime.of(2024, 1, 3, 12, 0)),
            createTestEntityWithDate(storeId, metricName, "day", LocalDateTime.of(2024, 1, 5, 12, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(any())).thenAnswer(invocation -> {
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = invocation.getArgument(0);
            return filledEntities.stream()
                .map(entity -> createTestDtoWithDate(
                    entity.getStoreId(), 
                    entity.getMetricName(), 
                    entity.getMetricDateType(), 
                    entity.getMetricDate(), 
                    entity.getMetricValue()))
                .collect(Collectors.toList());
        });

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then - Should have 5 days total (2024-01-01 to 2024-01-05)
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // Verify the dates are consecutive (1 day apart)
        for (int i = 0; i < result.size() - 1; i++) {
            LocalDateTime current = result.get(i).getMetricDate();
            LocalDateTime next = result.get(i + 1).getMetricDate();
            assertEquals(1, java.time.temporal.ChronoUnit.DAYS.between(current, next));
        }
        
        // Verify that dates are normalized to start of day
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), result.get(0).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 1, 2, 0, 0, 0), result.get(1).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 1, 3, 0, 0, 0), result.get(2).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 1, 4, 0, 0, 0), result.get(3).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 1, 5, 0, 0, 0), result.get(4).getMetricDate());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldFillMissingDailyData_RealScenario() {
        // Given - Real database scenario
        String storeId = "store-1";
        String metricName = "metric-1";
        
        // Create test entities with real database dates
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "day", LocalDateTime.of(2025, 7, 15, 15, 0, 16)),
            createTestEntityWithDate(storeId, metricName, "day", LocalDateTime.of(2025, 7, 20, 15, 5, 20)),
            createTestEntityWithDate(storeId, metricName, "day", LocalDateTime.of(2025, 7, 25, 14, 45, 47))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(any())).thenAnswer(invocation -> {
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = invocation.getArgument(0);
            return filledEntities.stream()
                .map(entity -> createTestDtoWithDate(
                    entity.getStoreId(), 
                    entity.getMetricName(), 
                    entity.getMetricDateType(), 
                    entity.getMetricDate(), 
                    entity.getMetricValue()))
                .collect(Collectors.toList());
        });

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then - Should have 11 days total (2025-07-15 to 2025-07-25)
        assertNotNull(result);
        assertEquals(11, result.size());
        
        // Verify the dates are consecutive (1 day apart)
        for (int i = 0; i < result.size() - 1; i++) {
            LocalDateTime current = result.get(i).getMetricDate();
            LocalDateTime next = result.get(i + 1).getMetricDate();
            assertEquals(1, java.time.temporal.ChronoUnit.DAYS.between(current, next));
        }
        
        // Verify original data is preserved (normalized to start of day)
        assertTrue(result.stream().anyMatch(dto -> 
            dto.getMetricDate().equals(LocalDateTime.of(2025, 7, 15, 0, 0, 0))));
        assertTrue(result.stream().anyMatch(dto -> 
            dto.getMetricDate().equals(LocalDateTime.of(2025, 7, 20, 0, 0, 0))));
        assertTrue(result.stream().anyMatch(dto -> 
            dto.getMetricDate().equals(LocalDateTime.of(2025, 7, 25, 0, 0, 0))));
        
        // Verify first and last dates
        assertEquals(LocalDateTime.of(2025, 7, 15, 0, 0, 0), result.get(0).getMetricDate());
        assertEquals(LocalDateTime.of(2025, 7, 25, 0, 0, 0), result.get(10).getMetricDate());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldFillMissingWeeklyData() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        
        // Create test entities with gaps in weekly data
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "week", LocalDateTime.of(2024, 1, 1, 12, 0)), // Monday
            createTestEntityWithDate(storeId, metricName, "week", LocalDateTime.of(2024, 1, 15, 12, 0)), // Monday
            createTestEntityWithDate(storeId, metricName, "week", LocalDateTime.of(2024, 1, 29, 12, 0))  // Monday
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(any())).thenAnswer(invocation -> {
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = invocation.getArgument(0);
            return filledEntities.stream()
                .map(entity -> createTestDtoWithDate(
                    entity.getStoreId(), 
                    entity.getMetricName(), 
                    entity.getMetricDateType(), 
                    entity.getMetricDate(), 
                    entity.getMetricValue()))
                .collect(Collectors.toList());
        });

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then - Should have 5 weeks total (2024-01-01, 2024-01-08, 2024-01-15, 2024-01-22, 2024-01-29)
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // Verify the dates are consecutive weeks (7 days apart)
        for (int i = 0; i < result.size() - 1; i++) {
            LocalDateTime current = result.get(i).getMetricDate();
            LocalDateTime next = result.get(i + 1).getMetricDate();
            assertEquals(7, java.time.temporal.ChronoUnit.DAYS.between(current, next));
        }
        
        // Verify that dates are normalized to Monday of each week
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), result.get(0).getMetricDate()); // Monday
        assertEquals(LocalDateTime.of(2024, 1, 8, 0, 0, 0), result.get(1).getMetricDate()); // Monday
        assertEquals(LocalDateTime.of(2024, 1, 15, 0, 0, 0), result.get(2).getMetricDate()); // Monday
        assertEquals(LocalDateTime.of(2024, 1, 22, 0, 0, 0), result.get(3).getMetricDate()); // Monday
        assertEquals(LocalDateTime.of(2024, 1, 29, 0, 0, 0), result.get(4).getMetricDate()); // Monday
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldFillMissingMonthlyData() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        
        // Create test entities with gaps in monthly data
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "month", LocalDateTime.of(2024, 1, 15, 12, 0)),
            createTestEntityWithDate(storeId, metricName, "month", LocalDateTime.of(2024, 3, 20, 12, 0)),
            createTestEntityWithDate(storeId, metricName, "month", LocalDateTime.of(2024, 5, 10, 12, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(any())).thenAnswer(invocation -> {
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = invocation.getArgument(0);
            return filledEntities.stream()
                .map(entity -> createTestDtoWithDate(
                    entity.getStoreId(), 
                    entity.getMetricName(), 
                    entity.getMetricDateType(), 
                    entity.getMetricDate(), 
                    entity.getMetricValue()))
                .collect(Collectors.toList());
        });

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then - Should have 5 months total (2024-01, 2024-02, 2024-03, 2024-04, 2024-05)
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // Verify the dates are consecutive months (1 month apart)
        for (int i = 0; i < result.size() - 1; i++) {
            LocalDateTime current = result.get(i).getMetricDate();
            LocalDateTime next = result.get(i + 1).getMetricDate();
            assertEquals(1, java.time.temporal.ChronoUnit.MONTHS.between(current, next));
        }
        
        // Verify that dates are normalized to first day of month
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), result.get(0).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 2, 1, 0, 0, 0), result.get(1).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 3, 1, 0, 0, 0), result.get(2).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 4, 1, 0, 0, 0), result.get(3).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 5, 1, 0, 0, 0), result.get(4).getMetricDate());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldFillMissingYearlyData() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        
        // Create test entities with gaps in yearly data
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "year", LocalDateTime.of(2022, 6, 15, 12, 0)),
            createTestEntityWithDate(storeId, metricName, "year", LocalDateTime.of(2024, 12, 20, 12, 0)),
            createTestEntityWithDate(storeId, metricName, "year", LocalDateTime.of(2026, 3, 10, 12, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(any())).thenAnswer(invocation -> {
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = invocation.getArgument(0);
            return filledEntities.stream()
                .map(entity -> createTestDtoWithDate(
                    entity.getStoreId(), 
                    entity.getMetricName(), 
                    entity.getMetricDateType(), 
                    entity.getMetricDate(), 
                    entity.getMetricValue()))
                .collect(Collectors.toList());
        });

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then - Should have 5 years total (2022, 2023, 2024, 2025, 2026)
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // Verify the dates are consecutive years (1 year apart)
        for (int i = 0; i < result.size() - 1; i++) {
            LocalDateTime current = result.get(i).getMetricDate();
            LocalDateTime next = result.get(i + 1).getMetricDate();
            assertEquals(1, java.time.temporal.ChronoUnit.YEARS.between(current, next));
        }
        
        // Verify that dates are normalized to first day of year
        assertEquals(LocalDateTime.of(2022, 1, 1, 0, 0, 0), result.get(0).getMetricDate());
        assertEquals(LocalDateTime.of(2023, 1, 1, 0, 0, 0), result.get(1).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), result.get(2).getMetricDate());
        assertEquals(LocalDateTime.of(2025, 1, 1, 0, 0, 0), result.get(3).getMetricDate());
        assertEquals(LocalDateTime.of(2026, 1, 1, 0, 0, 0), result.get(4).getMetricDate());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldHandleUnknownDateType() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "unknown", LocalDateTime.of(2024, 1, 1, 12, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);
        });
        assertThat(exception.getMessage()).contains("Unknown date type: unknown");
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldReturnEmptyList_WhenNoData() {
        // Given
        String storeId = "store-1";
        String metricName = "metric-1";
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(List.of());
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(List.of())).thenReturn(List.of());

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findContinuousMetricsByStoreIdAndMetricName_ShouldNormalizeDatesCorrectly() {
        // Given - Test that dates are normalized correctly for different types
        String storeId = "store-1";
        String metricName = "metric-1";
        
        // Test monthly data with different times on same month
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithDate(storeId, metricName, "month", LocalDateTime.of(2024, 1, 15, 10, 30, 0)),
            createTestEntityWithDate(storeId, metricName, "month", LocalDateTime.of(2024, 1, 20, 15, 45, 0)),
            createTestEntityWithDate(storeId, metricName, "month", LocalDateTime.of(2024, 3, 10, 9, 15, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricName(storeId, metricName)).thenReturn(entities);
        when(storeProfilingStoreContinuousMetricsDtoMapper.toDtoList(any())).thenAnswer(invocation -> {
            List<StoreProfilingStoreContinuousMetricsEntity> filledEntities = invocation.getArgument(0);
            return filledEntities.stream()
                .map(entity -> createTestDtoWithDate(
                    entity.getStoreId(), 
                    entity.getMetricName(), 
                    entity.getMetricDateType(), 
                    entity.getMetricDate(), 
                    entity.getMetricValue()))
                .collect(Collectors.toList());
        });

        // When
        List<StoreProfilingStoreContinuousMetricsDto> result = storeProfilingService.findContinuousMetricsByStoreIdAndMetricName(storeId, metricName);

        // Then - Should have 3 months total (2024-01, 2024-02, 2024-03)
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // Verify that dates are normalized to first day of month
        assertEquals(LocalDateTime.of(2024, 1, 1, 0, 0, 0), result.get(0).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 2, 1, 0, 0, 0), result.get(1).getMetricDate());
        assertEquals(LocalDateTime.of(2024, 3, 1, 0, 0, 0), result.get(2).getMetricDate());
        
        // Verify the dates are consecutive months
        for (int i = 0; i < result.size() - 1; i++) {
            LocalDateTime current = result.get(i).getMetricDate();
            LocalDateTime next = result.get(i + 1).getMetricDate();
            assertEquals(1, java.time.temporal.ChronoUnit.MONTHS.between(current, next));
        }
    }

    @Test
    void getStoreTags_whenNoTags_returnsEmptyList() {
        when(storeProfilingTagRepository.findAll()).thenReturn(List.of());
        List<String> tags = storeProfilingService.getStoreTags();
        assertThat(tags).isEmpty();
    }

    @Test
    void getStoreInfoByStoreId_ShouldReturnStoreInfoExtDto_WhenStoreExists() {
        // Given
        String storeId = "test-store-123";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        storeInfoDto.setStoreName("Test Store");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock discrete metrics
        List<StoreProfilingStoreMetricsEntity> metricsEntities = Arrays.asList(
            createMetricsEntity(storeId, "Basic", "Store Type", "Grocery"),
            createMetricsEntity(storeId, "Basic", "Store Source", "MERCASO"),
            createMetricsEntity(storeId, "Basic", "Store Open Hours", "09:00-21:00"),
            createMetricsEntity(storeId, "PO", "Grade", "A"),
            createMetricsEntity(storeId, "Alert", "AOV Alert", "High"),
            createMetricsEntity(storeId, "Basic", "Area Good for Families Grade", "B+")
        );
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(metricsEntities);
        
        // Mock mapper for metrics
        when(storeProfilingStoreMetricsDtoMapper.toDto(any())).thenAnswer(invocation -> {
            StoreProfilingStoreMetricsEntity entity = invocation.getArgument(0);
            StoreProfilingStoreMetricsDto dto = new StoreProfilingStoreMetricsDto();
            dto.setMetricCategory(entity.getMetricCategory());
            dto.setMetricName(entity.getMetricName());
            dto.setMetricValue(entity.getMetricValue());
            dto.setMetricDesc(entity.getMetricDesc());
            return dto;
        });
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreId()).isEqualTo(storeId);
        assertThat(result.getStoreName()).isEqualTo("Test Store");
        assertThat(result.getStoreType()).isEqualTo("Grocery");
        assertThat(result.getStoreSource()).isEqualTo("MERCASO");
        assertThat(result.getStoreOpenHours()).isEqualTo("09:00-21:00");
        assertThat(result.getStoreGrade()).isEqualTo("A");
        assertThat(result.getStoreAlertTags()).hasSize(1);
        assertThat(result.getStoreAround()).isNotNull();
        assertThat(result.getStoreAround().getGrades()).hasSize(1);
    }

    @Test
    void getStoreInfoByStoreId_ShouldThrowException_WhenStoreIdIsNull() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.getStoreInfoByStoreId(null);
        });
        assertThat(exception.getMessage()).contains("storeId is empty or null");
    }

    @Test
    void getStoreInfoByStoreId_ShouldThrowException_WhenStoreIdIsEmpty() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.getStoreInfoByStoreId("");
        });
        assertThat(exception.getMessage()).contains("storeId is empty or null");
    }

    @Test
    void getStoreInfoByStoreId_ShouldThrowException_WhenStoreNotFound() {
        // Given
        String storeId = "non-existent-store";
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.empty());
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeProfilingService.getStoreInfoByStoreId(storeId);
        });
        assertThat(exception.getMessage()).contains("Store info not found for storeId: " + storeId);
    }

    @Test
    void getStoreInfoByStoreId_ShouldHandleNoMetrics_WhenStoreExistsButNoMetrics() {
        // Given
        String storeId = "store-no-metrics";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        storeInfoDto.setStoreName("Store Without Metrics");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock empty metrics
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(List.of());
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreId()).isEqualTo(storeId);
        assertThat(result.getStoreName()).isEqualTo("Store Without Metrics");
        assertThat(result.getStoreType()).isNull();
        assertThat(result.getStoreGrade()).isNull();
        assertThat(result.getStoreAlertTags()).isNull();
        assertThat(result.getStoreAround()).isNull();
    }

    @Test
    void getStoreInfoByStoreId_ShouldHandleMercasoStoreSourceOrdering() {
        // Given
        String storeId = "test-store-mercaso";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock metrics with multiple store sources
        List<StoreProfilingStoreMetricsEntity> metricsEntities = Arrays.asList(
            createMetricsEntity(storeId, "Basic", "Store Source", "SHOPIFY"),
            createMetricsEntity(storeId, "Basic", "Store Source", "SQUARE"),
            createMetricsEntity(storeId, "Basic", "Store Source", "MERCASO")
        );
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(metricsEntities);
        
        // Mock mapper for metrics
        when(storeProfilingStoreMetricsDtoMapper.toDto(any())).thenAnswer(invocation -> {
            StoreProfilingStoreMetricsEntity entity = invocation.getArgument(0);
            StoreProfilingStoreMetricsDto dto = new StoreProfilingStoreMetricsDto();
            dto.setMetricCategory(entity.getMetricCategory());
            dto.setMetricName(entity.getMetricName());
            dto.setMetricValue(entity.getMetricValue());
            dto.setMetricDesc(entity.getMetricDesc());
            return dto;
        });
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreSource()).startsWith("MERCASO");
        assertThat(result.getStoreSource()).contains("SHOPIFY");
        assertThat(result.getStoreSource()).contains("SQUARE");
    }

    @Test
    void getStoreInfoByStoreId_ShouldSortAroundMetrics() {
        // Given
        String storeId = "test-store-around";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock metrics with around info in random order
        List<StoreProfilingStoreMetricsEntity> metricsEntities = Arrays.asList(
            createMetricsEntity(storeId, "Basic", "Area Weather Grade", "B"),
            createMetricsEntity(storeId, "Basic", "Area Good for Families Grade", "A"),
            createMetricsEntity(storeId, "Basic", "Median Home Value", "$500,000"),
            createMetricsEntity(storeId, "Basic", "Area Feeling", "Safe"),
            createMetricsEntity(storeId, "Basic", "Area Population", "50,000")
        );
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(metricsEntities);
        
        // Mock mapper for metrics
        when(storeProfilingStoreMetricsDtoMapper.toDto(any())).thenAnswer(invocation -> {
            StoreProfilingStoreMetricsEntity entity = invocation.getArgument(0);
            StoreProfilingStoreMetricsDto dto = new StoreProfilingStoreMetricsDto();
            dto.setMetricCategory(entity.getMetricCategory());
            dto.setMetricName(entity.getMetricName());
            dto.setMetricValue(entity.getMetricValue());
            dto.setMetricDesc(entity.getMetricDesc());
            return dto;
        });
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreAround()).isNotNull();
        
        // Verify grades are sorted by metric name
        List<StoreProfilingStoreMetricsDto> grades = result.getStoreAround().getGrades();
        assertThat(grades).hasSize(2);
        assertThat(grades.get(0).getMetricName()).isEqualTo("Area Good for Families Grade");
        assertThat(grades.get(1).getMetricName()).isEqualTo("Area Weather Grade");
        
        // Verify real estate stats are sorted
        List<StoreProfilingStoreMetricsDto> realEstate = result.getStoreAround().getRealEstateStats();
        assertThat(realEstate).hasSize(2);
        assertThat(realEstate.get(0).getMetricName()).isEqualTo("Area Feeling");
        assertThat(realEstate.get(1).getMetricName()).isEqualTo("Median Home Value");
        
        // Verify demographics are sorted
        List<StoreProfilingStoreMetricsDto> demographics = result.getStoreAround().getResidentDemographicsStats();
        assertThat(demographics).hasSize(1);
        assertThat(demographics.get(0).getMetricName()).isEqualTo("Area Population");
    }

    private StoreProfilingStoreMetricsEntity createMetricsEntity(String storeId, String category, String name, String value) {
        StoreProfilingStoreMetricsEntity entity = new StoreProfilingStoreMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricCategory(category);
        entity.setMetricName(name);
        entity.setMetricValue(value);
        entity.setMetricDesc("Test description for " + name);
        return entity;
    }

    @Test
    void findContinuousMetricsListByStoreIdAndCategory_ShouldReturnGroupedMetrics() {
        // Given
        String storeId = "store-1";
        ContinuousMetricsCategoryEnums category = ContinuousMetricsCategoryEnums.PO;
        
        // Use metric names that match the enum configuration
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithCategoryAndDate(storeId, "Monthly PO Amount", "PO", "day", LocalDateTime.of(2024, 1, 1, 0, 0)),
            createTestEntityWithCategoryAndDate(storeId, "Monthly PO Amount", "PO", "day", LocalDateTime.of(2024, 1, 2, 0, 0)),
            createTestEntityWithCategoryAndDate(storeId, "Monthly PO Frequency", "PO", "day", LocalDateTime.of(2024, 1, 1, 0, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO")))
            .thenReturn(entities);

        // When
        List<StoreProfilingContinuousMetricsListDto> result = storeProfilingService.findContinuousMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Two different metric names
        
        // Verify first metric (Monthly PO Amount)
        StoreProfilingContinuousMetricsListDto poAmount = result.stream()
            .filter(dto -> "Monthly PO Amount".equals(dto.getMetricName()))
            .findFirst().orElse(null);
        assertNotNull(poAmount);
        assertEquals(storeId, poAmount.getStoreId());
        assertEquals("PO", poAmount.getMetricCategory());
        assertEquals("Monthly PO Amount", poAmount.getMetricName());
        assertEquals(2, poAmount.getMetricValues().size());
        
        // Verify second metric (Monthly PO Frequency)
        StoreProfilingContinuousMetricsListDto poFrequency = result.stream()
            .filter(dto -> "Monthly PO Frequency".equals(dto.getMetricName()))
            .findFirst().orElse(null);
        assertNotNull(poFrequency);
        assertEquals("Monthly PO Frequency", poFrequency.getMetricName());
        assertEquals(1, poFrequency.getMetricValues().size());
    }

    @Test
    void findContinuousMetricsListByStoreIdAndCategory_ShouldReturnEmptyList_WhenNoData() {
        // Given
        String storeId = "store-1";
        ContinuousMetricsCategoryEnums category = ContinuousMetricsCategoryEnums.PO;
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO")))
            .thenReturn(List.of());

        // When
        List<StoreProfilingContinuousMetricsListDto> result = storeProfilingService.findContinuousMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findContinuousMetricsListByStoreIdAndCategory_ShouldHandleMultipleCategories() {
        // Given
        String storeId = "store-1";
        ContinuousMetricsCategoryEnums category = ContinuousMetricsCategoryEnums.TRANSACTION; // Use TRANSACTION which has multiple categories
        
        // Use metric names that match the TRANSACTION enum configuration
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithCategoryAndDate(storeId, "Monthly Sales Amount", "Sales", "day", LocalDateTime.of(2024, 1, 1, 0, 0)),
            createTestEntityWithCategoryAndDate(storeId, "Monthly Sales Quantity", "Sales", "day", LocalDateTime.of(2024, 1, 1, 0, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("Sales")))
            .thenReturn(entities);

        // When
        List<StoreProfilingContinuousMetricsListDto> result = storeProfilingService.findContinuousMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verify metrics from different categories are included
        assertTrue(result.stream().anyMatch(dto -> "Monthly Sales Amount".equals(dto.getMetricName())));
        assertTrue(result.stream().anyMatch(dto -> "Monthly Sales Quantity".equals(dto.getMetricName())));
    }

    @Test
    void findContinuousMetricsListByStoreIdAndCategory_ShouldSortMetricValuesByDateDescending() {
        // Given
        String storeId = "store-1";
        ContinuousMetricsCategoryEnums category = ContinuousMetricsCategoryEnums.PO;
        
        List<StoreProfilingStoreContinuousMetricsEntity> entities = Arrays.asList(
            createTestEntityWithCategoryAndDate(storeId, "Monthly PO Amount", "PO", "day", LocalDateTime.of(2024, 1, 1, 0, 0)),
            createTestEntityWithCategoryAndDate(storeId, "Monthly PO Amount", "PO", "day", LocalDateTime.of(2024, 1, 3, 0, 0)),
            createTestEntityWithCategoryAndDate(storeId, "Monthly PO Amount", "PO", "day", LocalDateTime.of(2024, 1, 2, 0, 0))
        );
        
        when(storeProfilingStoreContinuousMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO")))
            .thenReturn(entities);

        // When
        List<StoreProfilingContinuousMetricsListDto> result = storeProfilingService.findContinuousMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        StoreProfilingContinuousMetricsListDto dto = result.get(0);
        assertEquals(3, dto.getMetricValues().size());
        
        // Verify dates are sorted in descending order
        List<StoreProfilingContinuousMetricsListDto.MetricValueDto> values = dto.getMetricValues();
        assertTrue(values.get(0).getDate().isAfter(values.get(1).getDate()));
        assertTrue(values.get(1).getDate().isAfter(values.get(2).getDate()));
    }

    private StoreProfilingStoreContinuousMetricsEntity createTestEntityWithCategoryAndDate(String storeId, String metricName, String category, String dateType, LocalDateTime date) {
        StoreProfilingStoreContinuousMetricsEntity entity = new StoreProfilingStoreContinuousMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricCategory(category);
        entity.setMetricName(metricName);
        entity.setMetricDesc("test description");
        entity.setMetricValue(new BigDecimal("100.50"));
        entity.setMetricDate(date);
        entity.setMetricDateType(dateType);
        return entity;
    }

    // Tests for Discrete Metrics
    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldReturnGroupedMetrics() {
        // Given
        String storeId = "store-1";
        DiscreteMetricsCategoryEnums category = DiscreteMetricsCategoryEnums.ITEM;
        
        // Mock entities for "Most purchased item Item with highest purchase quantity" group
        List<StoreProfilingStoreMetricsEntity> entities = Arrays.asList(
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item", "iPhone"),
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item Unit", "150"),
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item SKU", "IP001"),
            createDiscreteMetricsEntity(storeId, "PO", "Top Spending Item", "MacBook"),
            createDiscreteMetricsEntity(storeId, "PO", "Top Spending Item SKU", "MB001"),
            createDiscreteMetricsEntity(storeId, "PO", "Top Spending Item Amount", "2500")
        );
        
        when(storeProfilingStoreMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO", "Inventory", "Sales")))
            .thenReturn(entities);

        // Create a mock ItemCategoryDto that returns the expected photoUrl
        ItemCategoryDto itemCategoryDto = new ItemCategoryDto();
        itemCategoryDto.setPhotoUrl("https://test.png");
        when(itemManagementServiceClientAdaptor.findItemsBySkuIn(anyList())).thenReturn(List.of(itemCategoryDto));

        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Two description groups
        
        // Verify first group: "Most purchased item Item with highest purchase quantity"
        StoreProfilingDiscreteMetricsDto mostPurchased = result.stream()
            .filter(dto -> "iPhone".equals(dto.getMetricsName()))
            .findFirst().orElse(null);
        assertNotNull(mostPurchased);
        assertEquals("iPhone", mostPurchased.getMetricsName()); // tag="name"
        assertEquals("150", mostPurchased.getMetricsValue()); // tag="value"
        assertEquals("https://test.png", mostPurchased.getLinkUrl()); // tag="sku"
        
        // Verify second group: "Top spending item Item with highest spending"
        StoreProfilingDiscreteMetricsDto topSpending = result.stream()
            .filter(dto -> "MacBook".equals(dto.getMetricsName()))
            .findFirst().orElse(null);
        assertNotNull(topSpending);
        assertEquals("MacBook", topSpending.getMetricsName()); // tag="name"
        assertEquals("2500", topSpending.getMetricsValue()); // tag="value"
        assertEquals("https://test.png", topSpending.getLinkUrl()); // tag="sku"
    }

    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldReturnEmptyList_WhenNoData() {
        // Given
        String storeId = "store-1";
        DiscreteMetricsCategoryEnums category = DiscreteMetricsCategoryEnums.ITEM;
        
        when(storeProfilingStoreMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO", "Inventory", "Sales")))
            .thenReturn(List.of());

        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldReturnEmptyList_WhenStoreIdIsNull() {
        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory(null, DiscreteMetricsCategoryEnums.ITEM);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldReturnEmptyList_WhenStoreIdIsEmpty() {
        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory("", DiscreteMetricsCategoryEnums.ITEM);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldReturnEmptyList_WhenCategoryIsNull() {
        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory("store-1", null);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldHandlePartialData() {
        // Given - Only some tags are present
        String storeId = "store-1";
        DiscreteMetricsCategoryEnums category = DiscreteMetricsCategoryEnums.ITEM;
        
        List<StoreProfilingStoreMetricsEntity> entities = Arrays.asList(
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item", "iPhone"), // only name, no value or sku
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item Unit", "150") // only value, no name or sku
        );
        
        when(storeProfilingStoreMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO", "Inventory", "Sales")))
            .thenReturn(entities);

        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        StoreProfilingDiscreteMetricsDto dto = result.get(0);
        assertEquals("iPhone", dto.getMetricsName()); // tag="name"
        assertEquals("150", dto.getMetricsValue()); // tag="value"
        assertEquals("", dto.getLinkUrl()); // tag="sku" not present, should be empty
    }

    @Test
    void findDiscreteMetricsListByStoreIdAndCategory_ShouldSortResultsByMetricsName() {
        // Given
        String storeId = "store-1";
        DiscreteMetricsCategoryEnums category = DiscreteMetricsCategoryEnums.ITEM;
        
        List<StoreProfilingStoreMetricsEntity> entities = Arrays.asList(
            // Group 1: Top Spending (should come after Most Purchased alphabetically)
            createDiscreteMetricsEntity(storeId, "PO", "Top Spending Item", "Zebra Product"),
            createDiscreteMetricsEntity(storeId, "PO", "Top Spending Item Amount", "10"),
            createDiscreteMetricsEntity(storeId, "PO", "Top Spending Item SKU", "ZP001"),
            // Group 2: Most Purchased (should come first alphabetically)
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item", "Apple Product"),
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item Unit", "100"),
            createDiscreteMetricsEntity(storeId, "PO", "Most Purchased Item SKU", "AP001")
        );
        
        when(storeProfilingStoreMetricsRepository.findByStoreIdAndMetricCategoryIn(storeId, List.of("PO", "Inventory", "Sales")))
            .thenReturn(entities);

        // When
        List<StoreProfilingDiscreteMetricsDto> result = storeProfilingService.findDiscreteMetricsListByStoreIdAndCategory(storeId, category);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Verify sorting by metricsName (Apple Product should come before Zebra Product)
        assertEquals("Apple Product", result.get(0).getMetricsName());
        assertEquals("Zebra Product", result.get(1).getMetricsName());
    }

    private StoreProfilingStoreMetricsEntity createDiscreteMetricsEntity(String storeId, String category, String metricName, String value) {
        StoreProfilingStoreMetricsEntity entity = new StoreProfilingStoreMetricsEntity();
        entity.setStoreId(storeId);
        entity.setMetricCategory(category);
        entity.setMetricName(metricName);
        entity.setMetricValue(value);
        entity.setMetricDesc("Test description for " + metricName);
        return entity;
    }

    // Tests for Store Radar functionality
    @Test
    void getStoreInfoByStoreId_ShouldIncludeStoreRadar_WhenRadarMetricsExist() {
        // Given
        String storeId = "test-store-radar";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        storeInfoDto.setStoreName("Test Store");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock radar metrics
        List<StoreProfilingStoreMetricsEntity> metricsEntities = Arrays.asList(
            createMetricsEntity(storeId, "PO", "PO Frequency Score", "85.5"),
            createMetricsEntity(storeId, "PO", "PO Date Count Score", "90.0"),
            createMetricsEntity(storeId, "PO", "PO Amount Score", "75.3"),
            createMetricsEntity(storeId, "PO", "PO AOV Score", "88.7"),
            createMetricsEntity(storeId, "PO", "PO Discount Score", "92.1")
        );
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(metricsEntities);
        
        // Mock mapper for metrics
        when(storeProfilingStoreMetricsDtoMapper.toDto(any())).thenAnswer(invocation -> {
            StoreProfilingStoreMetricsEntity entity = invocation.getArgument(0);
            StoreProfilingStoreMetricsDto dto = new StoreProfilingStoreMetricsDto();
            dto.setMetricCategory(entity.getMetricCategory());
            dto.setMetricName(entity.getMetricName());
            dto.setMetricValue(entity.getMetricValue());
            dto.setMetricDesc(entity.getMetricDesc());
            return dto;
        });
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreRadarList()).isNotNull();
        assertThat(result.getStoreRadarList()).hasSize(5);

        // 验证具体的雷达指标
        StoreProfilingStoreMetricsDto frequencyScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO Frequency Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(frequencyScore).isNotNull();
        assertThat(frequencyScore.getMetricValue()).isEqualTo("85.5");

        StoreProfilingStoreMetricsDto dateCountScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO Date Count Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(dateCountScore).isNotNull();
        assertThat(dateCountScore.getMetricValue()).isEqualTo("90.0");

        StoreProfilingStoreMetricsDto amountScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO Amount Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(amountScore).isNotNull();
        assertThat(amountScore.getMetricValue()).isEqualTo("75.3");

        StoreProfilingStoreMetricsDto aovScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO AOV Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(aovScore).isNotNull();
        assertThat(aovScore.getMetricValue()).isEqualTo("88.7");

        StoreProfilingStoreMetricsDto discountScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO Discount Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(discountScore).isNotNull();
        assertThat(discountScore.getMetricValue()).isEqualTo("92.1");
    }

    @Test
    void getStoreInfoByStoreId_ShouldIncludeStoreRadarWithDefaultValues_WhenRadarMetricsMissing() {
        // Given
        String storeId = "test-store-no-radar";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        storeInfoDto.setStoreName("Test Store");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock metrics without radar scores (only basic info)
        List<StoreProfilingStoreMetricsEntity> metricsEntities = Arrays.asList(
            createMetricsEntity(storeId, "Basic", "Store Type", "Grocery"),
            createMetricsEntity(storeId, "Basic", "Store Source", "MERCASO")
        );
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(metricsEntities);
        
        // Mock mapper for metrics
        when(storeProfilingStoreMetricsDtoMapper.toDto(any())).thenAnswer(invocation -> {
            StoreProfilingStoreMetricsEntity entity = invocation.getArgument(0);
            StoreProfilingStoreMetricsDto dto = new StoreProfilingStoreMetricsDto();
            dto.setMetricCategory(entity.getMetricCategory());
            dto.setMetricName(entity.getMetricName());
            dto.setMetricValue(entity.getMetricValue());
            dto.setMetricDesc(entity.getMetricDesc());
            return dto;
        });
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreRadarList()).isNotNull();
        // All radar scores should default to 0.0 when metrics are missing
        assertThat(result.getStoreRadarList().size()).isEqualTo(0);
    }

    @Test
    void getStoreInfoByStoreId_ShouldIncludeStoreRadarWithPartialValues_WhenSomeRadarMetricsExist() {
        // Given
        String storeId = "test-store-partial-radar";
        
        // Mock basic store info
        StoreProfilingStoreInfoEntity storeInfoEntity = MetricsStoreProfilingStoreInfoEntityMock.metricsStoreProfilingStoreInfoEntityMock();
        storeInfoEntity.setStoreId(storeId);
        when(storeProfilingStoreInfoRepository.findById(storeId)).thenReturn(Optional.of(storeInfoEntity));
        
        StoreProfilingStoreInfoDto storeInfoDto = new StoreProfilingStoreInfoDto();
        storeInfoDto.setStoreId(storeId);
        storeInfoDto.setStoreName("Test Store");
        when(storeProfilingStoreInfoDtoMapper.toDto(storeInfoEntity)).thenReturn(storeInfoDto);
        
        // Mock metrics with only some radar scores
        List<StoreProfilingStoreMetricsEntity> metricsEntities = Arrays.asList(
            createMetricsEntity(storeId, "PO", "PO Frequency Score", "65.5"),
            createMetricsEntity(storeId, "PO", "PO Amount Score", "82.3"),
            // Missing: PO Date Count Score, PO AOV Score, PO Discount Score
            createMetricsEntity(storeId, "Basic", "Store Type", "Grocery")
        );
        when(storeProfilingStoreMetricsRepository.findByStoreId(storeId)).thenReturn(metricsEntities);
        
        // Mock mapper for metrics
        when(storeProfilingStoreMetricsDtoMapper.toDto(any())).thenAnswer(invocation -> {
            StoreProfilingStoreMetricsEntity entity = invocation.getArgument(0);
            StoreProfilingStoreMetricsDto dto = new StoreProfilingStoreMetricsDto();
            dto.setMetricCategory(entity.getMetricCategory());
            dto.setMetricName(entity.getMetricName());
            dto.setMetricValue(entity.getMetricValue());
            dto.setMetricDesc(entity.getMetricDesc());
            return dto;
        });
        
        // When
        StoreProfilingStoreInfoExtDto result = storeProfilingService.getStoreInfoByStoreId(storeId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStoreRadarList()).isNotNull();
        assertThat(result.getStoreRadarList()).hasSize(2); // Only 2 metrics present
        
        // Present scores should have their values
        StoreProfilingStoreMetricsDto frequencyScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO Frequency Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(frequencyScore).isNotNull();
        assertThat(frequencyScore.getMetricValue()).isEqualTo("65.5");
        
        StoreProfilingStoreMetricsDto amountScore = result.getStoreRadarList().stream()
            .filter(metric -> "PO Amount Score".equals(metric.getMetricName()))
            .findFirst().orElse(null);
        assertThat(amountScore).isNotNull();
        assertThat(amountScore.getMetricValue()).isEqualTo("82.3");
        
        // Missing scores should not be present in the list
        boolean hasDateCountScore = result.getStoreRadarList().stream()
            .anyMatch(metric -> "PO Date Count Score".equals(metric.getMetricName()));
        assertThat(hasDateCountScore).isFalse();
        
        boolean hasAovScore = result.getStoreRadarList().stream()
            .anyMatch(metric -> "PO AOV Score".equals(metric.getMetricName()));
        assertThat(hasAovScore).isFalse();
        
        boolean hasDiscountScore = result.getStoreRadarList().stream()
            .anyMatch(metric -> "PO Discount Score".equals(metric.getMetricName()));
        assertThat(hasDiscountScore).isFalse();
    }
}
