package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.mapper.MasterCatalogTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogTaskServiceImpl;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;

@ExtendWith(MockitoExtension.class)
class MasterCatalogTaskServiceImplTest {

  @Mock
  private MasterCatalogTaskMapper masterCatalogTaskMapper;

  @Mock
  private MasterCatalogTaskRepository masterCatalogTaskRepository;

  @Mock
  private MasterCatalogPotentiallyDuplicateRawDataRepository duplicateRawDataRepository;

  @InjectMocks
  private MasterCatalogTaskServiceImpl masterCatalogTaskService;

  private MockedStatic<SecurityContextUtil> securityContextUtil;

  private UUID taskId;
  private UUID jobId;
  private MasterCatalogTask task;
  private MasterCatalogTaskDto taskDto;

  @BeforeEach
  void setUp() {
    taskId = UUID.randomUUID();
    jobId = UUID.randomUUID();
    securityContextUtil = Mockito.mockStatic(SecurityContextUtil.class);

    task = MasterCatalogTask.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    taskDto = MasterCatalogTaskDto.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();
  }

  @AfterEach
  void tearDown() {
    securityContextUtil.close();
  }

  @Test
  void createTasks_ShouldCreateTasksSuccessfully() {

    Integer taskCount = 3;
    List<MasterCatalogTask> savedTasks = List.of(task, task, task);

    when(masterCatalogTaskRepository.saveAll(anyList())).thenReturn(savedTasks);
    when(masterCatalogTaskMapper.toDto(any(MasterCatalogTask.class))).thenReturn(taskDto);

    List<MasterCatalogTaskDto> result = masterCatalogTaskService.createTasks(jobId,
        MasterCatalogTaskType.DUPLICATION_IN_BATCH, taskCount);

    // Then
    assertEquals(3, result.size());
    verify(masterCatalogTaskRepository).saveAll(argThat((List<MasterCatalogTask> tasks) ->
        tasks.size() == 3 &&
        tasks.stream().allMatch(t ->
            t.getJobId().equals(jobId) &&
            t.getStatus() == MasterCatalogTaskStatus.PENDING &&
            t.getType() == MasterCatalogTaskType.DUPLICATION_IN_BATCH
        )
    ));
    verify(masterCatalogTaskMapper, times(3)).toDto(any(MasterCatalogTask.class));
  }

  @Test
  void assign_WhenTaskExistsAndPending_ShouldAssignSuccessfully() {
    // Given
    String assignedTo = UUID.randomUUID().toString();
    String currentUserId = UUID.randomUUID().toString();

    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));
    when(masterCatalogTaskRepository.save(any(MasterCatalogTask.class))).thenReturn(task);

    securityContextUtil.when(SecurityContextUtil::getLoginUserId).thenReturn(currentUserId);

    masterCatalogTaskService.assign(taskId, assignedTo);

    verify(masterCatalogTaskRepository).save(argThat(savedTask ->
        assignedTo.equals(savedTask.getAssignedTo()) &&
        currentUserId.equals(savedTask.getAssignedBy()) &&
        savedTask.getStatus() == MasterCatalogTaskStatus.ASSIGNED
    ));
  }

  @Test
  void assign_WhenTaskNotFound_ShouldThrowException() {

    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.empty());

    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.assign(taskId, UUID.randomUUID().toString())
        , "task not fount with id");
  }

  @Test
  void assign_WhenTaskNotPending_ShouldThrowException() {

    task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));

    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.assign(taskId, UUID.randomUUID().toString())
        , "the task is not a available task");
  }

  @Test
  void search_WithNullParameters_ShouldReturnAllResults() {
    // Given
    PageRequest pageRequest = PageRequest.of(0, 10);
    Page<MasterCatalogTask> taskPage = mock(Page.class);
    Page<MasterCatalogTaskDto> dtoPage = mock(Page.class);

    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("admin_master_catalog"));
    when(masterCatalogTaskRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(taskPage);
    when(taskPage.map(any(Function.class))).thenReturn(dtoPage);
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null, null, null, null, null, pageRequest);
    
    // Then
    assertNotNull(result);
    verify(masterCatalogTaskRepository).findAll(any(Specification.class), eq(pageRequest));
  }

  @Test
  void search_WithEmptyStringAssignTo_ShouldIgnoreParameter() {
    // Given
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("admin_master_catalog"));
    PageRequest pageRequest = PageRequest.of(0, 10);
    Page<MasterCatalogTask> taskPage = mock(Page.class);
    Page<MasterCatalogTaskDto> dtoPage = mock(Page.class);
    
    when(masterCatalogTaskRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(taskPage);
    when(taskPage.map(any(Function.class))).thenReturn(dtoPage);
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null, null, "", MasterCatalogTaskStatus.PENDING, null, pageRequest);
    
    // Then
    assertNotNull(result);
    verify(masterCatalogTaskRepository).findAll(any(Specification.class), eq(pageRequest));
  }

  @Test
  void search_WithoutPermission_ShouldReturnEmptyPage() {
    // Given
    PageRequest pageRequest = PageRequest.of(0, 10);
    
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("other_role"));
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null, null, null, null, null, pageRequest);
    
    // Then
    assertTrue(result.isEmpty());
    verify(masterCatalogTaskRepository, never()).findAll(any(Specification.class), any(PageRequest.class));
  }

  @Test
  void search_WithReviewerRole_ShouldFilterByCurrentUser() {
    // Given
    String currentUserId = "reviewer123";
    PageRequest pageRequest = PageRequest.of(0, 10);
    Page<MasterCatalogTask> taskPage = mock(Page.class);
    Page<MasterCatalogTaskDto> dtoPage = mock(Page.class);
    
    securityContextUtil.when(SecurityContextUtil::getMercasoRoles)
        .thenReturn(List.of("admin_master_catalog_reviewer"));
    securityContextUtil.when(SecurityContextUtil::getLoginUserId)
        .thenReturn(currentUserId);
    when(masterCatalogTaskRepository.findAll(any(Specification.class), eq(pageRequest))).thenReturn(taskPage);
    when(taskPage.map(any(Function.class))).thenReturn(dtoPage);
    
    // When
    Page<MasterCatalogTaskDto> result = masterCatalogTaskService.search(null, null, null, null, null, pageRequest);
    
    // Then
    assertNotNull(result);
    verify(masterCatalogTaskRepository).findAll(any(Specification.class), eq(pageRequest));
  }

  @Test
  void getDetailsById_WithValidId_ShouldReturnTaskWithDetails() {

    UUID taskId = UUID.randomUUID();
    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();
    
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));
    when(masterCatalogTaskMapper.toDto(task)).thenReturn(taskDto);
    when(duplicateRawDataRepository.findByTaskId(taskId)).thenReturn(Collections.emptyList());

    MasterCatalogTaskDto result = masterCatalogTaskService.getDetailsById(taskId);

    assertNotNull(result);
    assertEquals(0, result.getTotalRecordCount());
    verify(masterCatalogTaskRepository).findById(taskId);
    verify(duplicateRawDataRepository).findByTaskId(taskId);
  }

  @Test
  void getDetailsById_WithNonExistentId_ShouldThrowException() {
    // Given
    UUID taskId = UUID.randomUUID();
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.empty());

    // When & Then
    assertThrows(Exception.class, () -> masterCatalogTaskService.getDetailsById(taskId));
  }
}
