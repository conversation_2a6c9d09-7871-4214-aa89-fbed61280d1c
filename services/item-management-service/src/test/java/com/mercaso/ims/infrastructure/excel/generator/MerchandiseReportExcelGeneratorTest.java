package com.mercaso.ims.infrastructure.excel.generator;

import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.google.common.collect.Maps;
import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.itempromoprice.service.ItemPromoPriceService;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.service.ItemRegPriceService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.excel.data.MerchandiseReportData;
import com.mercaso.ims.utils.brand.BrandUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.itemregprice.ItemRegPriceUtil;
import com.mercaso.ims.utils.vendor.VendorItemUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class MerchandiseReportExcelGeneratorTest {

    @Mock
    VendorItemService vendorItemService;
    @Mock
    ItemRegPriceService itemRegPriceService;
    @Mock
    ItemPromoPriceService itemPromoPriceService;
    @Mock
    DocumentApplicationService documentApplicationService;
    @Mock
    FeatureFlagsManager featureFlagsManager;
    @Mock
    CategoryApplicationService categoryApplicationService;
    @InjectMocks
    MerchandiseReportExcelGenerator merchandiseReportExcelGenerator;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGenerateReport() {
        byte[] result = merchandiseReportExcelGenerator.generateReport(List.of(new MerchandiseReportData()));
        Assertions.assertTrue(result.length > 0);
    }


    @Test
    void testMapItemsToReportData() {

        UUID id = UUID.randomUUID();
        UUID brandId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        String sku = RandomStringUtils.randomAlphabetic(8);
        Map<String, Long> sales4WOSMap = new HashMap<>();
        sales4WOSMap.put(sku, 1L);
        Map<String, Long> sales1WOSMap = new HashMap<>();
        sales1WOSMap.put(sku, 1L);
        Brand brand = BrandUtil.buildBrand(brandId);
        Map<UUID, Brand> brandMap = new HashMap<>();
        brandMap.put(brandId, brand);
        Vendor vendor = VendorUtil.buildVendor(vendorId, VendorConstant.VERNON_SALES);

        Map<UUID, Vendor> vendorMap = new HashMap<>();
        vendorMap.put(vendorId, vendor);

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        itemDto.setSkuNumber(sku);
        itemDto.setPrimaryVendorId(vendorId);
        itemDto.setId(id);

        String vendorSkuNumber = RandomStringUtils.randomAlphabetic(6);

        VendorItem vendorItem = VendorItemUtil.buildVendorItem(vendorSkuNumber, vendorId, id);
        ItemRegPrice regPrice = ItemRegPriceUtil.buildItemRegPrice(id);

        when(vendorItemService.findByItemIds(anyList())).thenReturn(List.of(vendorItem));
        when(itemRegPriceService.findByItemIds(anyList())).thenReturn(List.of(regPrice));
        when(itemPromoPriceService.findByItemIds(anyList())).thenReturn(new ArrayList<>());
        when(documentApplicationService.getImsUrl(anyString())).thenReturn("getImsUrlResponse");

        List<MerchandiseReportData> result = merchandiseReportExcelGenerator.mapItemsToReportData(List.of(itemDto),
            List.of(new FinaleAvailableStockDto()),
            sales4WOSMap,
            sales1WOSMap,
            brandMap,
            vendorMap, Maps.newHashMap());
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(sku, result.get(0).getSku());
        Assertions.assertEquals(vendorSkuNumber, result.get(0).getVernonItemNumber());


    }

}