package com.mercaso.ims.application.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.api.client.util.Maps;
import com.mercaso.data.client.dto.FinaleAvailableStockDto;
import com.mercaso.data.client.dto.ItemQuantityDto;
import com.mercaso.data.client.dto.WeeklyItemSalesDto;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.document.operations.operations.DocumentOperations;
import com.mercaso.ims.application.command.BulkExportRecordsCommand;
import com.mercaso.ims.application.dto.BulkExportRecordsDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.payload.BulkExportRecordsPayloadDto;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import com.mercaso.ims.application.service.BulkExportRecordsApplicationService;
import com.mercaso.ims.application.service.CategoryApplicationService;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.attribute.Attribute;
import com.mercaso.ims.domain.attribute.service.AttributeService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.brand.service.BrandService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.excel.data.MerchandiseReportData;
import com.mercaso.ims.infrastructure.excel.generator.MerchandiseReportExcelGenerator;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.FinaleAdaptor;
import com.mercaso.ims.infrastructure.external.google.GoogleDriverAdaptor;
import com.mercaso.ims.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.ims.utils.attribute.AttributeUtil;
import com.mercaso.ims.utils.brand.BrandUtil;
import com.mercaso.ims.utils.finale.FinaleAvailableStockDtoUtil;
import com.mercaso.ims.utils.item.ItemDTOUtil;
import com.mercaso.ims.utils.vendor.VendorUtil;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executor;

import org.apache.catalina.connector.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

class MerchandiseReportServiceImplTest {

    @Mock
    MerchandiseReportExcelGenerator excelGenerator;
    @Mock
    DocumentOperations documentOperations;
    @Mock
    DocumentApplicationService documentApplicationService;
    @Mock
    ItemQueryApplicationService itemQueryService;
    @Mock
    GoogleDriverAdaptor googleDriverAdaptor;
    @Mock
    ShopifyAdaptor shopifyAdaptor;
    @Mock
    FinaleAdaptor finaleAdaptor;
    @Mock
    BrandService brandService;
    @Mock
    AttributeService attributeService;
    @Mock
    VendorService vendorService;
    @Mock
    CategoryApplicationService categoryApplicationService;
    @InjectMocks
    MerchandiseReportServiceImpl merchandiseReportServiceImpl;
    @Mock
    private ItemSearchApplicationService itemSearchApplicationService;
    @Mock
    private BusinessEventService businessEventService;
    @Mock
    private BulkExportRecordsApplicationService bulkExportRecordsApplicationService;
    @Mock
    private Executor taskExecutor;



    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Configure taskExecutor to run synchronously for testing
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // Execute synchronously
            return null;
        }).when(taskExecutor).execute(any(Runnable.class));
    }

    @Test
    void testGetMerchandiseReportFile() {
        UUID brandId = UUID.randomUUID();
        UUID attributeId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        String sku = "sku_number";

        ItemQuantityDto itemQuantityDto = new ItemQuantityDto();
        itemQuantityDto.setItem(sku);
        itemQuantityDto.setQuantity(1L);
        WeeklyItemSalesDto weeklyItemSalesDto = new WeeklyItemSalesDto();
        weeklyItemSalesDto.setFourWeeklyData(List.of(itemQuantityDto));
        weeklyItemSalesDto.setWeeklyData(List.of(itemQuantityDto));
        Brand brand = BrandUtil.buildBrand(brandId);

        Attribute attribute = AttributeUtil.buildAttribute(attributeId);

        Vendor vendor = VendorUtil.buildVendor(vendorId);

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        itemDto.setSkuNumber(sku);

        Page<ItemDto> page = new PageImpl<ItemDto>(List.of(itemDto));
        MerchandiseReportData merchandiseReportData = new MerchandiseReportData();
        merchandiseReportData.setSku(sku);

        FinaleAvailableStockDto finaleAvailableStockDto = FinaleAvailableStockDtoUtil.buildFinaleAvailableStockDto(sku);

        when(excelGenerator.generateReport(any())).thenReturn(new byte[]{(byte) 0});
        when(excelGenerator.mapItemsToReportData(any(),
            any(),
            any(),
            any(),
            any(),
            any(), any())).thenReturn(
            List.of(merchandiseReportData));
        when(itemQueryService.findPagedItems(any(Pageable.class))).thenReturn(page);
        when(documentOperations.uploadDocument(any(UploadDocumentRequest.class))).thenReturn(new DocumentResponse("signedUrl",
            "name"));
        when(documentApplicationService.uploadExcel(any(), any())).thenReturn(new DocumentResponse("signedUrl",
            "name"));
        when(shopifyAdaptor.getWeeklySkuSalesData()).thenReturn(weeklyItemSalesDto);
        when(finaleAdaptor.getAllProducts()).thenReturn(List.of(finaleAvailableStockDto));
        when(brandService.findAll()).thenReturn(List.of(brand));
        when(attributeService.findAll()).thenReturn(List.of(attribute));
        when(vendorService.findAll()).thenReturn(List.of(vendor));
        when(categoryApplicationService.getAllLeafNodesWithAncestors()).thenReturn(Maps.newHashMap());

        merchandiseReportServiceImpl.getMerchandiseReportFile();
        verify(googleDriverAdaptor).uploadFileToGoogleDriver(anyString(), any(byte[].class));
    }

    @Test
    void testFetchAllItemsConcurrently() {
        UUID brandId = UUID.randomUUID();
        UUID attributeId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        String sku = "sku_number";
        Map<String, Long> sales4WOSMap = new HashMap<>();
        sales4WOSMap.put(sku, 1L);
        Map<String, Long> sales1WOSMap = new HashMap<>();
        sales1WOSMap.put(sku, 1L);
        Brand brand = BrandUtil.buildBrand(brandId);
        Map<UUID, Brand> brandMap = new HashMap<>();
        brandMap.put(brandId, brand);
        Attribute attribute = AttributeUtil.buildAttribute(attributeId);
        Map<UUID, Attribute> attributeMap = new HashMap<>();
        attributeMap.put(attributeId, attribute);

        Vendor vendor = VendorUtil.buildVendor(vendorId);
        Map<UUID, Vendor> vendorMap = new HashMap<>();
        vendorMap.put(vendorId, vendor);

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        itemDto.setSkuNumber(sku);

        Page<ItemDto> page = new PageImpl<ItemDto>(List.of(itemDto));
        MerchandiseReportData merchandiseReportData = new MerchandiseReportData();
        merchandiseReportData.setSku(sku);

        when(excelGenerator.generateReport(any())).thenReturn(new byte[]{(byte) 0});
        when(excelGenerator.mapItemsToReportData(any(),
            any(),
            any(),
            any(),
            any(),
            any(), any())).thenReturn(
            List.of(merchandiseReportData));
        when(itemQueryService.findPagedItems(any(Pageable.class))).thenReturn(page);

        FinaleAvailableStockDto dto = FinaleAvailableStockDtoUtil.buildFinaleAvailableStockDto(sku);
        List<MerchandiseReportData> result = merchandiseReportServiceImpl.fetchAllItemsConcurrently(List.of(dto),
            sales4WOSMap,
            sales1WOSMap,
            brandMap,
            vendorMap, Maps.newHashMap(), null);
        Assertions.assertEquals(1, result.size());
        Assertions.assertEquals(sku, result.getFirst().getSku());
    }

    @Test
    void testDownloadFilteredMerchandiseReport() {
        // Given
        String customFilter = "{\"categoryId\":\"Electronics\"}";
        UUID brandId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        String sku = "sku_number";
        UUID bulkExportRecordsId = UUID.randomUUID();

        // Setup test data
        ItemQuantityDto itemQuantityDto = new ItemQuantityDto();
        itemQuantityDto.setItem(sku);
        itemQuantityDto.setQuantity(1L);

        WeeklyItemSalesDto weeklyItemSalesDto = new WeeklyItemSalesDto();
        weeklyItemSalesDto.setFourWeeklyData(List.of(itemQuantityDto));
        weeklyItemSalesDto.setWeeklyData(List.of(itemQuantityDto));

        Brand brand = BrandUtil.buildBrand(brandId);
        Vendor vendor = VendorUtil.buildVendor(vendorId);

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        itemDto.setSkuNumber(sku);

        Page<ItemDto> page = new PageImpl<>(List.of(itemDto));
        MerchandiseReportData merchandiseReportData = new MerchandiseReportData();
        merchandiseReportData.setSku(sku);

        FinaleAvailableStockDto finaleAvailableStockDto = FinaleAvailableStockDtoUtil.buildFinaleAvailableStockDto(sku);

        BulkExportRecordsDto bulkExportRecordsDto = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .customFilter(customFilter)
            .fileName("documents/Custom_Filter_Merchandise_Report_test.xlsx")
            .exportBy("testUser")
            .searchTime(Instant.now())
            .build();

        // Configure mocks
        when(excelGenerator.generateReport(any())).thenReturn(new byte[]{(byte) 0});
        when(excelGenerator.mapItemsToReportData(any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(List.of(merchandiseReportData));
        when(itemSearchApplicationService.searchItemListIds(any())).thenReturn(List.of(UUID.randomUUID()));
        when(itemSearchApplicationService.searchItemCount(any())).thenReturn(1L);
        when(itemQueryService.findByIdIn(any())).thenReturn(List.of(itemDto));
        when(itemQueryService.findPagedItems(any(Pageable.class))).thenReturn(page);
        when(documentApplicationService.uploadExcel(any(), any()))
            .thenReturn(new DocumentResponse("signedUrl", "name"));
        when(shopifyAdaptor.getWeeklySkuSalesData()).thenReturn(weeklyItemSalesDto);
        when(finaleAdaptor.getAllProducts()).thenReturn(List.of(finaleAvailableStockDto));
        when(brandService.findAll()).thenReturn(List.of(brand));
        when(vendorService.findAll()).thenReturn(List.of(vendor));
        when(categoryApplicationService.getAllLeafNodesWithAncestors()).thenReturn(Maps.newHashMap());
        when(bulkExportRecordsApplicationService.save(any())).thenReturn(bulkExportRecordsDto);

        // When - Call the method (now runs synchronously due to mocked executor)
        merchandiseReportServiceImpl.downloadFilteredMerchandiseReport(customFilter);

        // Then
        // Verify that bulkExportRecordsApplicationService.save was called with correct command
        ArgumentCaptor<BulkExportRecordsCommand> commandCaptor = ArgumentCaptor.forClass(BulkExportRecordsCommand.class);
        verify(bulkExportRecordsApplicationService).save(commandCaptor.capture());

        BulkExportRecordsCommand capturedCommand = commandCaptor.getValue();
        Assertions.assertEquals(customFilter, capturedCommand.getCustomFilter());
        Assertions.assertTrue(capturedCommand.getFileName().startsWith("documents/"));
        Assertions.assertTrue(capturedCommand.getFileName().contains("Custom_Filter_Merchandise_Report"));
        Assertions.assertTrue(capturedCommand.getFileName().endsWith(".xlsx"));
        Assertions.assertNotNull(capturedCommand.getSearchTime());

        // Verify that businessEventService.dispatch was called with the correct parameters
        ArgumentCaptor<BulkExportRecordsPayloadDto> payloadCaptor = ArgumentCaptor.forClass(BulkExportRecordsPayloadDto.class);
        verify(businessEventService).dispatch(payloadCaptor.capture());

        BulkExportRecordsPayloadDto capturedPayload = payloadCaptor.getValue();
        Assertions.assertEquals(bulkExportRecordsId, capturedPayload.getBulkExportRecordsId());
        Assertions.assertEquals(bulkExportRecordsDto, capturedPayload.getData());

        // Verify that the report generation process was triggered
        verify(shopifyAdaptor).getWeeklySkuSalesData();
        verify(finaleAdaptor).getAllProducts();
        verify(brandService).findAll();
        verify(vendorService).findAll();
        verify(categoryApplicationService).getAllLeafNodesWithAncestors();
        verify(excelGenerator).generateReport(any());
        verify(documentApplicationService).uploadExcel(any(), anyString());
    }

    @Test
    void testDownloadFilteredMerchandiseReport_withNullCustomFilter() {
        // Given
        String customFilter = null;
        UUID brandId = UUID.randomUUID();
        UUID vendorId = UUID.randomUUID();
        String sku = "sku_number";
        UUID bulkExportRecordsId = UUID.randomUUID();

        // Setup test data
        ItemQuantityDto itemQuantityDto = new ItemQuantityDto();
        itemQuantityDto.setItem(sku);
        itemQuantityDto.setQuantity(1L);

        WeeklyItemSalesDto weeklyItemSalesDto = new WeeklyItemSalesDto();
        weeklyItemSalesDto.setFourWeeklyData(List.of(itemQuantityDto));
        weeklyItemSalesDto.setWeeklyData(List.of(itemQuantityDto));

        Brand brand = BrandUtil.buildBrand(brandId);
        Vendor vendor = VendorUtil.buildVendor(vendorId);

        ItemDto itemDto = ItemDTOUtil.buildItemDTO();
        itemDto.setSkuNumber(sku);

        Page<ItemDto> page = new PageImpl<>(List.of(itemDto));
        MerchandiseReportData merchandiseReportData = new MerchandiseReportData();
        merchandiseReportData.setSku(sku);

        FinaleAvailableStockDto finaleAvailableStockDto = FinaleAvailableStockDtoUtil.buildFinaleAvailableStockDto(sku);

        BulkExportRecordsDto bulkExportRecordsDto = BulkExportRecordsDto.builder()
            .id(bulkExportRecordsId)
            .customFilter(customFilter)
            .fileName("documents/Custom_Filter_Merchandise_Report_test.xlsx")
            .exportBy("testUser")
            .searchTime(Instant.now())
            .build();

        // Configure mocks
        when(excelGenerator.generateReport(any())).thenReturn(new byte[]{(byte) 0});
        when(excelGenerator.mapItemsToReportData(any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(List.of(merchandiseReportData));
        when(itemQueryService.findPagedItems(any(Pageable.class))).thenReturn(page);
        when(documentApplicationService.uploadExcel(any(), any()))
            .thenReturn(new DocumentResponse("signedUrl", "name"));
        when(shopifyAdaptor.getWeeklySkuSalesData()).thenReturn(weeklyItemSalesDto);
        when(finaleAdaptor.getAllProducts()).thenReturn(List.of(finaleAvailableStockDto));
        when(brandService.findAll()).thenReturn(List.of(brand));
        when(vendorService.findAll()).thenReturn(List.of(vendor));
        when(categoryApplicationService.getAllLeafNodesWithAncestors()).thenReturn(Maps.newHashMap());
        when(bulkExportRecordsApplicationService.save(any())).thenReturn(bulkExportRecordsDto);

        // When - Call the method (now runs synchronously due to mocked executor)
        merchandiseReportServiceImpl.downloadFilteredMerchandiseReport(customFilter);

        // Then
        // Verify that bulkExportRecordsApplicationService.save was called with null custom filter
        ArgumentCaptor<BulkExportRecordsCommand> commandCaptor = ArgumentCaptor.forClass(BulkExportRecordsCommand.class);
        verify(bulkExportRecordsApplicationService).save(commandCaptor.capture());

        BulkExportRecordsCommand capturedCommand = commandCaptor.getValue();
        Assertions.assertNull(capturedCommand.getCustomFilter());
        Assertions.assertTrue(capturedCommand.getFileName().startsWith("documents/"));
        Assertions.assertNotNull(capturedCommand.getSearchTime());

        // Verify that businessEventService.dispatch was called
        verify(businessEventService).dispatch(any(BulkExportRecordsPayloadDto.class));

        // Verify that itemQueryService.findPagedItems was called instead of search service
        verify(itemQueryService).findPagedItems(any(Pageable.class));
        // Verify that search service was NOT called when customFilter is null
        verify(itemSearchApplicationService, Mockito.never()).searchItemListIds(any());
    }
}