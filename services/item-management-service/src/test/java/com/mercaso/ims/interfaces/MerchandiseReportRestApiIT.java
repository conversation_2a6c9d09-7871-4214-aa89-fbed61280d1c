package com.mercaso.ims.interfaces;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.data.client.dto.WeeklyItemSalesDto;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.service.DocumentApplicationService;
import java.util.Collections;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

@Disabled
class MerchandiseReportRestApiIT extends AbstractIT {

    @MockBean
    private DocumentApplicationService documentApplicationService;


    @BeforeEach
    void setUp() {

        when(finaleAdaptor.getAllProducts()).thenReturn(Collections.emptyList());
        when(shopifyAdaptor.getWeeklySkuSalesData()).thenReturn(new WeeklyItemSalesDto());

        when(excelGenerator.generateReport(any())).thenReturn("Sample report content".getBytes());
    }

    @Test
    void shouldSuccessWhenDownloadMerchandiseReport() throws Exception {
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("Version_08302024_Mercaso Merchandise Report.xlsx");
        documentResponse.setSignedUrl("testUrl");

        when(documentOperations.uploadDocument(any())).thenReturn(documentResponse);
        when(documentOperations.downloadDocument(any())).thenReturn("Sample report content".getBytes());

        DocumentResponse result = merchandiseReportRestApiUtil.downloadMerchandiseReport();

        Assertions.assertNotNull(result);
        Assertions.assertEquals("Version_08302024_Mercaso Merchandise Report.xlsx", result.getName());
    }

    @Test
    void shouldSuccessWhenDownloadFilteredMerchandiseReport() throws Exception {
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("Version_20240830120000_Custom_Filter_Merchandise_Report.xlsx");
        documentResponse.setSignedUrl("testUrl");

        when(documentApplicationService.uploadExcel(any(), anyString())).thenReturn(documentResponse);

        merchandiseReportRestApiUtil.downloadFilteredMerchandiseReport("brand:Test Brand");

        // Since the method returns void, we verify that the document upload was performed
        verify(documentApplicationService).uploadExcel(any(), anyString());
    }

    @Test
    void shouldSuccessWhenDownloadFilteredMerchandiseReportWithoutFilter() throws Exception {
        DocumentResponse documentResponse = new DocumentResponse();
        documentResponse.setName("Version_20240830120000_Custom_Filter_Merchandise_Report.xlsx");
        documentResponse.setSignedUrl("testUrl");

        when(documentApplicationService.uploadExcel(any(), anyString())).thenReturn(documentResponse);

        merchandiseReportRestApiUtil.downloadFilteredMerchandiseReport(null);

        // Since the method returns void, we verify that the document upload was performed
        verify(documentApplicationService).uploadExcel(any(), anyString());
    }
}