package com.mercaso.ims.domain.itemsalestrend.service;

import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;

import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface ItemSalesTrendService {

    List<ItemSalesTrend> findByItemIdAndTimeGrain(UUID itemId, ItemSalesTrendTimeGrain timeGrain);

    /**
     * Find sales trends by item IDs and date range with DAY time grain
     * @param itemIds list of item IDs
     * @param startDate start date (inclusive)
     * @param endDate end date (inclusive)
     * @return list of sales trends
     */
    List<ItemSalesTrend> findByItemIdsAndTimeDimBetween(List<String> itemIds, Date startDate, Date endDate);

}
