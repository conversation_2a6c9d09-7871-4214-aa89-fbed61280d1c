package com.mercaso.ims.infrastructure.schedule;


import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleCompletedPurchaseOrderReportDto;
import com.mercaso.ims.infrastructure.external.finale.dto.CompletedPurchaseOrderDto;
import com.mercaso.ims.infrastructure.external.finale.dto.CompletedPurchaseOrderItemDto;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinalePurchaseOderScheduler {

    private static final Integer SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY = "[FinalePurchaseOderScheduler.syncFinalePurchaseOrders]".hashCode();

    private final PgAdvisoryLock pgAdvisoryLock;
    private final VendorService vendorService;
    private final ItemCostCollectionApplicationService itemCostCollectionApplicationService;
    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    private final ItemCostCollectionService itemCostCollectionService;
    private final EntityManagerFactory managerFactory;
    private final DocumentApplicationService documentApplicationService;

    private static final DateTimeFormatter INPUT_FORMATTER =
            new DateTimeFormatterBuilder()
                    .parseCaseInsensitive()
                    .appendPattern("MMM dd yyyy h:mm:ss a")
                    .toFormatter(Locale.ENGLISH);
    private static final DateTimeFormatter OUTPUT_FORMATTER =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final String FILE_PREFIX = "finale-po-";


    @Scheduled(fixedRate = 2 * 60 * 60 * 1000)
    public void syncFinalePurchaseOrders() {
        log.info(" Starting [SyncFinalePO] Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            if (!tryAcquireLock(entityManager)) {
                log.warn("[SyncFinalePO] Already in progress, exiting...");
                return;
            }

            List<FinaleCompletedPurchaseOrderReportDto> completedPurchaseOrdersReports = finaleExternalApiAdaptor.getCompletedPurchaseOrdersReport();
            

            if (CollectionUtils.isEmpty(completedPurchaseOrdersReports)) {
                log.info("[SyncFinalePO] No purchase orders found from Finale");
                return;
            }

            List<CompletedPurchaseOrderDto> groupedOrders = groupPurchaseOrdersByOrderId(completedPurchaseOrdersReports);

            groupedOrders.stream()
                    .filter(CompletedPurchaseOrderDto::isReceived)
                    .filter(completedPurchaseOrderDto -> !completedPurchaseOrderDto.isJitPurchaseOrder())
                    .filter(CompletedPurchaseOrderDto::isPurchaseType)
                    .filter(this::isValidPurchaseOrder)
                    .forEach(this::processPurchaseOrder);

        } catch (Exception e) {
            log.error("[SyncFinalePO] Exception occurred: {}", e.getMessage(), e);
        } finally {
            releaseLock(entityManager);
            entityManager.close();
            log.info("Finished [SyncFinalePO] Task ");
        }

    }

    private boolean tryAcquireLock(EntityManager entityManager) {
        return Boolean.TRUE.equals(pgAdvisoryLock.tryLockWithSessionLevel(
                entityManager, SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY, "SyncFinalePO"));
    }

    private void releaseLock(EntityManager entityManager) {
        pgAdvisoryLock.unLock(entityManager, SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY, "Unlock SyncFinalePO");
    }

    private boolean isValidPurchaseOrder(CompletedPurchaseOrderDto po) {
        return StringUtils.isNotBlank(po.getOrderId())
                && po.getSupplierPartyId() != null;
    }

    private void processPurchaseOrder(CompletedPurchaseOrderDto po) {
        String orderId = po.getOrderId();
        String vendorFinaleId = po.getSupplierPartyId();
        log.info("[SyncFinalePO] Processing orderId: {}, vendorFinaleId: {}", orderId, vendorFinaleId);
        createPOItemCostCollection(po);
    }

    private List<CompletedPurchaseOrderDto> groupPurchaseOrdersByOrderId(List<FinaleCompletedPurchaseOrderReportDto> reportDtos) {
        Map<String, List<FinaleCompletedPurchaseOrderReportDto>> groupedByOrderId = reportDtos.stream()
                .collect(Collectors.groupingBy(FinaleCompletedPurchaseOrderReportDto::getOrderId));
        
        return groupedByOrderId.values().stream()
                .map(finaleCompletedPurchaseOrderReportDtos -> {
                    FinaleCompletedPurchaseOrderReportDto first = finaleCompletedPurchaseOrderReportDtos.getFirst();
                    List<CompletedPurchaseOrderItemDto> items = finaleCompletedPurchaseOrderReportDtos.stream()
                            .map(dto -> CompletedPurchaseOrderItemDto.builder()
                                    .supplierProductId(dto.getSupplierProductId())
                                    .productId(dto.getProductId())
                                    .packing(dto.getPacking())
                                    .pricePerUnit(dto.getPricePerUnit())
                                    .amount(dto.getAmount())
                                    .productUnitsOrdered(dto.getProductUnitsOrdered())
                                    .description(dto.getDescription())
                                    .build())
                            .toList();

                    return CompletedPurchaseOrderDto.builder()
                            .orderId(first.getOrderId())
                            .status(first.getStatus())
                            .orderDate(first.getOrderDate())
                            .apptDate(first.getApptDate())
                            .apptTime(first.getApptTime())
                            .recordLastUpdated(first.getRecordLastUpdated())
                            .recordLastUpdatedUser(first.getRecordLastUpdatedUser())
                            .dueDate(first.getDueDate())
                            .customer(first.getCustomer())
                            .supplierPartyId(first.getSupplierPartyId())
                            .supplier(first.getSupplier())
                            .shipmentsStatusSummary(first.getShipmentsStatusSummary())
                            .shipmentsSummary(first.getShipmentsSummary())
                            .type(first.getType())
                            .items(items)
                            .build();
                }).toList();
    }

    private void createPOItemCostCollection(CompletedPurchaseOrderDto order) {
        String orderId = order.getOrderId();
        String vendorFinaleId = order.getSupplierPartyId();
        String lastUpdatedDate = order.getRecordLastUpdated();
        String formattedDate = formatRecordLastUpdated(lastUpdatedDate);

        log.info(
                "[SyncFinalePO] Creating item cost collection for Finale PO orderId: {}, lastUpdatedDate:{}, vendorFinaleId: {}",
                orderId,
                lastUpdatedDate,
                vendorFinaleId);

        Vendor vendor = vendorService.findByFinaleId(vendorFinaleId);
        if (vendor == null) {
            log.warn("[SyncFinalePO] Vendor not found for finaleId: {}", vendorFinaleId);
            return;
        }

        String purchaseOrderNumber = orderId + "-" + formattedDate;

        List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());

        if (itemCostCollections.isEmpty() || itemCostCollections.stream().noneMatch(
                                itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                                                .equals(purchaseOrderNumber))) {

            // Convert order to JSON string and upload to S3
            String orderJson = SerializationUtils.serialize(order);
            byte[] jsonBytes = orderJson.getBytes(StandardCharsets.UTF_8);
            String fileName = FILE_PREFIX + purchaseOrderNumber;

            DocumentResponse documentResponse = documentApplicationService.uploadFileContent(jsonBytes, fileName, Boolean.FALSE);
            log.info("[SyncFinalePO] Uploaded order JSON to S3: {}", documentResponse.getName());

            CreateItemCostCollectionCommand command =
                    CreateItemCostCollectionCommand.builder()
                            .vendorId(vendor.getId())
                            .vendorName(vendor.getVendorName())
                            .source(ItemCostCollectionSources.FINALE_PURCHASE_ORDER)
                            .type(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER)
                            .vendorCollectionNumber(purchaseOrderNumber)
                            .fileName(documentResponse.getName())
                            .build();

            log.info("[SyncFinalePO] Creating item cost collection : {}", command);

            itemCostCollectionApplicationService.create(command);
        }
    }

    private String formatRecordLastUpdated(String recordLastUpdated) {
        LocalDateTime dateTime = LocalDateTime.parse(recordLastUpdated, INPUT_FORMATTER);
        return dateTime.format(OUTPUT_FORMATTER);
    }
}
