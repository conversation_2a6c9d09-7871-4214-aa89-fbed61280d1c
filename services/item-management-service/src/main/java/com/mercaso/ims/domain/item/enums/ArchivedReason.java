package com.mercaso.ims.domain.item.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ArchivedReason {
    DUPLICATE("DUPLICATE"),
    DISCONTINUED("DISCONTINUED"),
    HUMAN_ERROR("HUMAN ERROR"),
    CONFIGURATION_CHANGE("CONFIGURATION CHANGE"),
    LOW_SALES_PERFORMANCE("LOW SALES PERFORMANCE"),
    OLD_ARCHIVE("OLD ARCHIVE"),
    ZOMBIE("ZOMBIE"),
    MERCASO_RESTRICTION("MERCASO RESTRICTION"),
    UNKNOWN("UNKNOWN"),

    ;

    private String description;

    ArchivedReason(String description) {
        this.description = description;
    }

    @JsonCreator
    public static ArchivedReason fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equalsIgnoreCase(name)).findFirst().orElse(UNKNOWN);
    }

    public static ArchivedReason fromDescriptionString(String description) {
        return Arrays.stream(values()).filter(v -> v.getDescription().equalsIgnoreCase(description)).findFirst().orElse(UNKNOWN);
    }

}
