package com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.query.ItemVendorRebateQuery;
import com.mercaso.ims.infrastructure.repository.DynamicSearchCondition;
import com.mercaso.ims.infrastructure.repository.SearchConditionResolver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomizedItemVendorRebateJpaDaoImpl implements CustomizedItemVendorRebateJpaDao {

    private final JdbcTemplate jdbcTemplate;

    private static final String QUERY_ITEM_VENDOR_REBATE =
            """
               select ivr.id, ivr.vendor_item_id, ivr.vendor_id, ivr.item_id, 
               ivr.start_date, ivr.end_date, ivr.rebate_per_unit, v.vendor_name, i.sku_number
                   from item_vendor_rebate ivr
                            left join vendor v on ivr.vendor_id = v.id
                            left join item i on i.id = ivr.item_id
                   where ivr.deleted_at is null
                     and v.deleted_at is null
                     and i.deleted_at is null
                     and ivr.start_date <= :endDate
                     and (ivr.end_date IS NULL OR ivr.end_date >= :startDate)
                     and v.id = :vendorId
        """;

    private static final String QUERY_ITEM_VENDOR_REBATE_COUNT =
            """
               select count(*)
                   from item_vendor_rebate ivr
                            left join vendor v on ivr.vendor_id = v.id
                            left join item i on i.id = ivr.item_id
                   where ivr.deleted_at is null
                     and v.deleted_at is null
                     and i.deleted_at is null
                     and ivr.start_date <= :endDate
                     and (ivr.end_date IS NULL OR ivr.end_date >= :startDate)
                     and v.id = :vendorId
        """;

    @Override
    public List<ItemVendorRebateDto> findByVendorIdAndDateRange(ItemVendorRebateQuery query) {
        DynamicSearchCondition<ItemVendorRebateQuery> dynamicSearch = new ItemVendorRebateCustomFilterDynamicSearch();
        StringBuilder sql = new StringBuilder(QUERY_ITEM_VENDOR_REBATE);
        sql.append(dynamicSearch.generateConditionBlock(query));
        sql.append(query.offsetLimitSql());

        return jdbcTemplate.query(sql.toString(),
                ps -> dynamicSearch.bindSqlParameter(ps, query),
                new RowMapper<ItemVendorRebateDto>() {
                    @Override
                    public ItemVendorRebateDto mapRow(ResultSet rs, int rowNum) throws SQLException {
                        ItemVendorRebateDto itemVendorRebateDto = new ItemVendorRebateDto();
                        itemVendorRebateDto.setId(UUID.fromString(rs.getString("id")));
                        itemVendorRebateDto.setVendorItemId(UUID.fromString(rs.getString("vendor_item_id")));
                        itemVendorRebateDto.setItemId(UUID.fromString(rs.getString("item_id")));
                        itemVendorRebateDto.setVendorId(UUID.fromString(rs.getString("vendor_id")));
                        itemVendorRebateDto.setVendorName(rs.getString("vendor_name"));
                        itemVendorRebateDto.setSkuNumber(rs.getString("sku_number"));
                        Date startDate = rs.getDate("start_date");
                        itemVendorRebateDto.setStartDate(startDate!= null ? startDate.toLocalDate() : null);
                        Date endDate = rs.getDate("end_date");
                        itemVendorRebateDto.setEndDate(endDate!= null ? endDate.toLocalDate() : null);
                        itemVendorRebateDto.setRebatePerUnit(rs.getBigDecimal("rebate_per_unit"));
                        return itemVendorRebateDto;
                    }
                });
    }

    @Override
    public long countQuery(ItemVendorRebateQuery query) {
        DynamicSearchCondition<ItemVendorRebateQuery> dynamicSearch = new ItemVendorRebateCustomFilterDynamicSearch();
        StringBuilder sql = new StringBuilder(QUERY_ITEM_VENDOR_REBATE_COUNT);
        sql.append(dynamicSearch.generateConditionBlock(query));

        List<Long> countQuery = jdbcTemplate.query(sql.toString(),
                ps -> dynamicSearch.bindSqlParameter(ps, query),
                (rs, rowNum) -> rs.getLong(1));

        return countQuery.getFirst();
    }

    static class ItemVendorRebateCustomFilterDynamicSearch extends DynamicSearchCondition<ItemVendorRebateQuery> {

        public ItemVendorRebateCustomFilterDynamicSearch() {
            super(List.of(new RebateTimeRangeCondition()));
        }
    }

    private static class RebateTimeRangeCondition implements SearchConditionResolver<ItemVendorRebateQuery> {

        @Override
        public String generateConditionBlock(ItemVendorRebateQuery query) {
            if (null == query.getStartDate() || null == query.getEndDate()) {
                return StringUtils.EMPTY;
            }
            return " and ivr.start_date <= ? and (ivr.end_date IS NULL OR ivr.end_date >= ?) ";
        }

        @Override
        public int bindSqlParameter(PreparedStatement ps, ItemVendorRebateQuery query, int index) throws SQLException {

            if (null != query.getEndDate()) {
                ps.setString(index++, query.getEndDate().toString());
            }

            if (null != query.getStartDate()) {
                ps.setString(index++, query.getStartDate().toString());
            }

            return index;
        }
    }

}
