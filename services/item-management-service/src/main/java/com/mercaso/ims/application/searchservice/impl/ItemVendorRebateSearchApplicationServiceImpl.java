package com.mercaso.ims.application.searchservice.impl;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.dto.ItemVendorRebateListDto;
import com.mercaso.ims.application.query.ItemVendorRebateQuery;
import com.mercaso.ims.application.searchservice.ItemVendorRebateSearchApplicationService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.CustomizedItemVendorRebateJpaDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RequiredArgsConstructor
@Service
public class ItemVendorRebateSearchApplicationServiceImpl implements ItemVendorRebateSearchApplicationService {

    private final CustomizedItemVendorRebateJpaDao customizedItemVendorRebateJpaDao;

    private final AtomicInteger idGen = new AtomicInteger(1);

    private final int nThreads = Runtime.getRuntime().availableProcessors() * 2 + 1;

    private final ExecutorService rebateSearchThreadPool =
            new ThreadPoolExecutor(nThreads, nThreads,
                    0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(200),
                    r -> new Thread(r, "payout-search-thread-pool-" + idGen.getAndIncrement()),
                    new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public ItemVendorRebateListDto findByVendorIdAndDateRange(ItemVendorRebateQuery query) {

        log.info("[findByVendorIdAndDateRange] param itemQuery: {}.", query);
        Future<List<ItemVendorRebateDto>> itemVendorRebateDtoFuture = rebateSearchThreadPool.submit(() -> {
            return customizedItemVendorRebateJpaDao.findByVendorIdAndDateRange(query);
        });

        Future<Long> itemDtoCountFuture = rebateSearchThreadPool.submit(() -> customizedItemVendorRebateJpaDao.countQuery(query));

        try {
            return ItemVendorRebateListDto.builder().data(itemVendorRebateDtoFuture.get())
                    .totalCount(itemDtoCountFuture.get())
                    .build();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread was interrupted: ", e);
            throw new ImsBusinessException("Concurrency error for querying item list.");
        } catch (ExecutionException e) {
            log.error("Concurrency error for querying item list: ", e);
            throw new ImsBusinessException("Concurrency error for querying item list.");
        }
    }
}
