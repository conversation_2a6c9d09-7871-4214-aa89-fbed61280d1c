package com.mercaso.ims.domain.itemsalestrend;

import com.mercaso.ims.domain.BaseDomainRepository;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface ItemSalesTrendRepository extends BaseDomainRepository<ItemSalesTrend, UUID> {

    List<ItemSalesTrend> findByItemIdAndTimeGrain(String itemId, ItemSalesTrendTimeGrain timeGrain);

    /**
     * Find sales trends by item IDs and date range with DAY time grain
     * @param itemIds list of item IDs
     * @param startDate start date (inclusive)
     * @param endDate end date (inclusive)
     * @return list of sales trends
     */
    List<ItemSalesTrend> findByItemIdsAndTimeDimBetween(List<String> itemIds, Date startDate, Date endDate);

}
