package com.mercaso.ims.domain.itemsalestrend.service.impl;

import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrendRepository;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.domain.itemsalestrend.service.ItemSalesTrendService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ItemSalesTrendServiceImpl implements ItemSalesTrendService {

    private final ItemSalesTrendRepository itemSalesTrendRepository;

    @Override
    public List<ItemSalesTrend> findByItemIdAndTimeGrain(UUID itemId, ItemSalesTrendTimeGrain timeGrain) {
        return itemSalesTrendRepository.findByItemIdAndTimeGrain(itemId.toString(), timeGrain);
    }

    @Override
    public List<ItemSalesTrend> findByItemIdsAndTimeDimBetween(List<String> itemIds, Date startDate, Date endDate) {
        return itemSalesTrendRepository.findByItemIdsAndTimeDimBetween(itemIds, startDate, endDate);
    }
}
