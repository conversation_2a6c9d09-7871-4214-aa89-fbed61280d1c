package com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.query.ItemVendorRebateQuery;

import java.util.List;


public interface CustomizedItemVendorRebateJpaDao {

    List<ItemVendorRebateDto> findByVendorIdAndDateRange(ItemVendorRebateQuery query);

    long countQuery(ItemVendorRebateQuery query);

}
