package com.mercaso.ims.application.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.PageQuery;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.VENDOR_REBATE_LIST_SEARCH_JSON_PROCESSING_EXCEPTION;

@Getter
@Setter
@SuperBuilder
public class ItemVendorRebateQuery extends PageQuery {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    private Map<String, String> customFilter;


    private UUID vendorId;

    private LocalDate startDate;

    private LocalDate endDate;


    public UUID getVendorId() {
        return UUID.fromString(customFilter.get("vendorId"));
    }

    public LocalDate getStartDate() {
        return parseDate(customFilter.get("startDate"));
    }

    public LocalDate getEndDate() {
        return parseDate(customFilter.get("endDate"));
    }

    public static Map<String, String> getCustomFilterValue(String customFilterJson) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> customFilter = new HashMap<>();
        if (customFilterJson != null) {
            try {
                customFilter = objectMapper.readValue(customFilterJson, new TypeReference<Map<String, String>>() {
                });
            } catch (JsonProcessingException e) {
                throw new ImsBusinessException(VENDOR_REBATE_LIST_SEARCH_JSON_PROCESSING_EXCEPTION);
            }
        }
        return customFilter;
    }


    /**
     * Validates that all required fields are present
     * @return true if valid, false otherwise
     */
    public boolean isValid() {
        return startDate != null && endDate != null && !startDate.isAfter(endDate);
    }

    /**
     * Gets validation error message
     * @return error message if invalid, null if valid
     */
    public String getValidationError() {
        if (startDate == null) {
            return "Start date is required for rebate export";
        }
        if (endDate == null) {
            return "End date is required for rebate export";
        }
        if (startDate.isAfter(endDate)) {
            return "Start date must be before or equal to end date";
        }
        return null;
    }

    /**
     * Parses date string to LocalDate safely
     */
    private LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, FORMATTER);
        } catch (DateTimeParseException e) {
            return null;
        }
    }
}
