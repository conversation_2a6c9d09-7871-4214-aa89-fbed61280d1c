package com.mercaso.ims.infrastructure.repository.itemsalestrend;

import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrend;
import com.mercaso.ims.domain.itemsalestrend.ItemSalesTrendRepository;
import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa.ItemSalesTrendJpaDao;
import com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa.dataobject.ItemSalesTrendDo;
import com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa.mapper.ItemSalesTrendDoMapper;
import com.mercaso.ims.infrastructure.util.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

@Component
@RequiredArgsConstructor
public class ItemSalesTrendRepositoryImpl implements ItemSalesTrendRepository {

    private final ItemSalesTrendJpaDao itemSalesTrendJpaDao;

    private final ItemSalesTrendDoMapper itemSalesTrendDoMapper;


    @Override
    public ItemSalesTrend save(ItemSalesTrend domain) {
        ItemSalesTrendDo doObj = itemSalesTrendDoMapper.domainToDo(domain);
        doObj = itemSalesTrendJpaDao.save(doObj);
        return itemSalesTrendDoMapper.doToDomain(doObj);
    }

    @Override
    public ItemSalesTrend findById(UUID id) {
        ItemSalesTrendDo doObj = itemSalesTrendJpaDao.findById(id).orElse(null);
        if (null == doObj) {
            return null;
        }
        return itemSalesTrendDoMapper.doToDomain(doObj);
    }

    @Override
    public ItemSalesTrend update(ItemSalesTrend domain) {
        ItemSalesTrendDo doObj = itemSalesTrendJpaDao.findById(domain.getId()).orElse(null);
        if (Objects.isNull(doObj)) {
            throw new ImsBusinessException(ErrorCodeEnums.ITEM_COST_COLLECTION_NOT_FOUND);
        }
        ItemSalesTrendDo doObjTarget = itemSalesTrendDoMapper.domainToDo(domain);
        List<String> ignoreProperties = Arrays.asList("createdBy", "createdAt");
        BeanUtils.copyProperties(doObjTarget, doObj, ignoreProperties.toArray(new String[0]));
        ItemSalesTrendDo result = itemSalesTrendJpaDao.save(doObj);
        return itemSalesTrendDoMapper.doToDomain(result);
    }

    @Override
    public ItemSalesTrend deleteById(UUID id) {
        ItemSalesTrendDo doObj = itemSalesTrendJpaDao.findById(id).orElse(null);
        if (null == doObj) {
            return null;
        }
        doObj.setDeletedAt(Instant.now());
        doObj.setDeletedBy(SecurityUtil.getLoginUserId());
        doObj.setDeletedUserName(SecurityUtil.getUserName());
        doObj = itemSalesTrendJpaDao.save(doObj);
        return itemSalesTrendDoMapper.doToDomain(doObj);
    }

    @Override
    public List<ItemSalesTrend> findByItemIdAndTimeGrain(String itemId, ItemSalesTrendTimeGrain timeGrain) {
        return Optional.ofNullable(itemSalesTrendJpaDao.findByItemIdAndTimeGrain(itemId, timeGrain))
                .orElse(Collections.emptyList())
                .stream()
                .map(itemSalesTrendDoMapper::doToDomain)
                .toList().reversed();
    }

    @Override
    public List<ItemSalesTrend> findByItemIdsAndTimeDimBetween(List<String> itemIds, Date startDate, Date endDate) {
        if (itemIds == null || itemIds.isEmpty()) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(itemSalesTrendJpaDao.findByItemIdInAndTimeDimBetweenAndTimeGrain(itemIds, startDate, endDate))
                .orElse(Collections.emptyList())
                .stream()
                .map(itemSalesTrendDoMapper::doToDomain)
                .toList();
    }
}
