package com.mercaso.ims.infrastructure.excel.data;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * Excel data structure for Rebate Report export
 * Contains rebate calculation results for suppliers
 */
@Getter
@Setter
@EqualsAndHashCode
public class RebateReportData {

    @ExcelProperty("Supplier Name")
    @ColumnWidth(30)
    private String supplierName;

    @ExcelProperty("SKU")
    @ColumnWidth(20)
    private String sku;

    @ExcelProperty("Description")
    @ColumnWidth(50)
    private String description;

    @NumberFormat("$0.00")
    @ExcelProperty("Per Case Rebate")
    @ColumnWidth(15)
    private BigDecimal perCaseRebate;

    @ExcelProperty("Units Sold")
    @ColumnWidth(15)
    private Integer unitsSold;

    @NumberFormat("$0.00")
    @ExcelProperty("Total Rebate")
    @ColumnWidth(15)
    private BigDecimal totalRebate;
}
