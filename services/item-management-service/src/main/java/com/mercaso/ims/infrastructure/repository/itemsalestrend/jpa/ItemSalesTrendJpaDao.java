package com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa;

import com.mercaso.ims.domain.itemsalestrend.enums.ItemSalesTrendTimeGrain;
import com.mercaso.ims.infrastructure.repository.itemsalestrend.jpa.dataobject.ItemSalesTrendDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.UUID;

public interface ItemSalesTrendJpaDao extends JpaRepository<ItemSalesTrendDo, UUID> {

    List<ItemSalesTrendDo> findByItemIdAndTimeGrain(String itemId, ItemSalesTrendTimeGrain timeGrain);

    /**
     * Find sales trends by item IDs and date range with DAY time grain
     */
    @Query("SELECT s FROM ItemSalesTrendDo s WHERE s.itemId IN :itemIds " +
           "AND s.timeDim BETWEEN :startDate AND :endDate " +
           "AND s.timeGrain = 'DAY' " +
           "ORDER BY s.itemId, s.timeDim")
    List<ItemSalesTrendDo> findByItemIdInAndTimeDimBetweenAndTimeGrain(
            @Param("itemIds") List<String> itemIds,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

}
