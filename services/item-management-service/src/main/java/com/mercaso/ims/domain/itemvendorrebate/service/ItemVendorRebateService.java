package com.mercaso.ims.domain.itemvendorrebate.service;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import java.util.List;
import java.util.UUID;

/**
 * Domain service for ItemVendorRebate business logic
 */

public interface ItemVendorRebateService {

    ItemVendorRebate save(ItemVendorRebate itemVendorRebate);

    ItemVendorRebate update(ItemVendorRebate itemVendorRebate);

    void delete(UUID id);

    ItemVendorRebate findById(UUID id);

    List<ItemVendorRebate> findByVendorItemId (UUID vendorItemId);
}
