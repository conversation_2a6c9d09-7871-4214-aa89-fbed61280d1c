package com.mercaso.wms.infrastructure.alert.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * WMS Alert Event DTO for creating and managing WMS alert events
 * Currently focused on ORDER_AFTER_BATCH_CREATION scenario but designed for extensibility
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WmsExceptionAlertDto extends BaseDto {

    private AlertEventType eventType;
    private AlertSeverity severity;
    private String title;
    private String description;
    private UUID relatedEntityId;
    private AlertEntityType relatedEntityType;
    private Map<String, Object> contextData;
    private Instant detectedAt;

    /**
     * Creates an alert for order created after batch scenario
     */
    public static WmsExceptionAlertDto createOrderAfterBatchAlert(
        String orderNumber,
        UUID orderId,
        String batchNumber,
        UUID batchId,
        LocalDate deliveryDate) {

        Map<String, Object> context = Map.of(
            "orderNumber", orderNumber,
            "batchNumber", batchNumber,
            "batchId", batchId.toString(),
            "deliveryDate", deliveryDate.toString()
        );

        return WmsExceptionAlertDto.builder()
            .eventType(AlertEventType.ORDER_AFTER_BATCH_CREATION)
            .severity(AlertSeverity.CRITICAL)
            .title("Order Created After Batch")
            .description(String.format(
                "Order %s was created after batch %s was already created for delivery date %s",
                orderNumber, batchNumber, deliveryDate))
            .relatedEntityId(orderId)
            .relatedEntityType(AlertEntityType.SHIPPING_ORDER)
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .build();
    }

    /**
     * Creates an alert for picking tasks incomplete after 8PM LA time
     */
    public static WmsExceptionAlertDto createPickingTasksIncompleteAlert(
        UUID batchId,
        String batchNumber,
        String vendorName,
        String source,
        String deliveryDate,
        List<Map<String, Object>> incompleteTaskDetails) {

        Map<String, Object> context = Map.of(
            "batchId", batchId.toString(),
            "batchNumber", batchNumber != null ? batchNumber : "N/A",
            "vendorName", vendorName,
            "source", source,
            "deliveryDate", deliveryDate,
            "totalIncompleteTasks", incompleteTaskDetails.size(),
            "incompleteTaskDetails", incompleteTaskDetails,
            "alertTime", DateUtils.getNowInLA().toString()
        );

        return WmsExceptionAlertDto.builder()
            .eventType(AlertEventType.PICKING_TASKS_INCOMPLETE_AFTER_8PM)
            .severity(AlertSeverity.HIGH)
            .title("Picking Tasks Incomplete After 8PM")
            .description(String.format(
                "Batch %s has %d incomplete picking tasks for vendor %s after 8PM LA time",
                batchNumber != null ? batchNumber : batchId.toString(),
                incompleteTaskDetails.size(),
                vendorName))
            .relatedEntityId(batchId)
            .relatedEntityType(AlertEntityType.BATCH)
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .build();
    }

    /**
     * Creates an alert for ghost inventory cleanup failure
     */
    public static WmsExceptionAlertDto createGhostInventoryCleanupFailureAlert(
        UUID deliveryOrderId,
        String orderNumber,
        String deliveryItemsSummary,
        String errorMessage) {

        Map<String, Object> context = new HashMap<>();
        context.put("deliveryOrderId", deliveryOrderId.toString());
        context.put("orderNumber", orderNumber != null ? orderNumber : "N/A");
        context.put("deliveryItems", deliveryItemsSummary != null ? deliveryItemsSummary : "NO_ITEMS");
        context.put("errorMessage", errorMessage != null ? errorMessage : "Unknown error");
        context.put("alertTime", DateUtils.getNowInLA().toString());
        context.put("requiresRecovery", true);

        return WmsExceptionAlertDto.builder()
            .eventType(AlertEventType.GHOST_INVENTORY_CLEANUP_FAILED)
            .severity(AlertSeverity.HIGH)
            .title("Ghost Inventory Cleanup Failed")
            .description(String.format(
                "Failed to clean ghost inventory for delivered order %s (ID: %s). Manual recovery required.",
                orderNumber != null ? orderNumber : "N/A",
                deliveryOrderId))
            .relatedEntityId(deliveryOrderId)
            .relatedEntityType(AlertEntityType.SHIPPING_ORDER)
            .contextData(context)
            .detectedAt(DateUtils.getNowInLA())
            .build();
    }

    @Getter
    public enum AlertEventType {
        ORDER_AFTER_BATCH_CREATION("ORDER_AFTER_BATCH_CREATION", "Order Created After Batch"),
        PICKING_TASKS_INCOMPLETE_AFTER_8PM("PICKING_TASKS_INCOMPLETE_AFTER_8PM", "Picking Tasks Incomplete After 8PM"),
        GHOST_INVENTORY_CLEANUP_FAILED("GHOST_INVENTORY_CLEANUP_FAILED", "Ghost Inventory Cleanup Failed");
        // Future alert types can be added here:
        // BATCH_PROCESSING_FAILURE("BATCH_PROCESSING_FAILURE", "Batch Processing Failure"),
        // INVENTORY_SHORTAGE("INVENTORY_SHORTAGE", "Inventory Shortage");

        private final String value;
        private final String displayName;

        AlertEventType(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }
    }

    @Getter
    public enum AlertSeverity {
        CRITICAL("CRITICAL", "Critical", "🔴"),
        HIGH("HIGH", "High", "🟠"),
        MEDIUM("MEDIUM", "Medium", "🟡"),
        LOW("LOW", "Low", "🟢");

        private final String value;
        private final String displayName;
        private final String emoji;

        AlertSeverity(String value, String displayName, String emoji) {
            this.value = value;
            this.displayName = displayName;
            this.emoji = emoji;
        }
    }

    @Getter
    public enum AlertEntityType {
        SHIPPING_ORDER("SHIPPING_ORDER", "Shipping Order"),
        BATCH("BATCH", "Batch"),
        INVENTORY("INVENTORY", "Inventory"),
        WAREHOUSE("WAREHOUSE", "Warehouse");

        private final String value;
        private final String displayName;

        AlertEntityType(String value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }
    }
} 