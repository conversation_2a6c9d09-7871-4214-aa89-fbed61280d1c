package com.mercaso.wms.infrastructure.repository.customer;

import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.customeraddress.CustomerAddressRepository;
import com.mercaso.wms.infrastructure.repository.customer.jpa.CustomerAddressJpaDao;
import com.mercaso.wms.infrastructure.repository.customer.jpa.mapper.CustomerAddressDoMapper;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class CustomerAddressRepositoryImpl implements CustomerAddressRepository {

    private final CustomerAddressJpaDao jpaDao;
    private final CustomerAddressDoMapper mapper;

    @Override
    public CustomerAddress findBySha1hex(String sha1hex) {
        return mapper.doToDomain(jpaDao.findBySha1hex(sha1hex));
    }

    @Override
    public List<CustomerAddress> findByLatitudeAndLongitude(BigDecimal latitude, BigDecimal longitude) {
        return mapper.doToDomains(jpaDao.findByLatitudeAndLongitude(latitude, longitude));
    }

    @Override
    public List<CustomerAddress> findByCityAndAddressOne(String city, String addressOne) {
        return mapper.doToDomains(jpaDao.findByCityAndAddressOneIgnoreCase(city, addressOne));
    }

    @Override
    public List<CustomerAddress> findByEmail(String email) {
        return mapper.doToDomains(jpaDao.findByEmail(email));
    }

    @Override
    public CustomerAddress save(CustomerAddress domain) {
        return mapper.doToDomain(jpaDao.save(mapper.domainToDo(domain)));
    }

    @Override
    public CustomerAddress findById(UUID id) {
        return mapper.doToDomain(jpaDao.findById(id).orElse(null));
    }

    @Override
    public CustomerAddress update(CustomerAddress domain) {
        return null;
    }

}
