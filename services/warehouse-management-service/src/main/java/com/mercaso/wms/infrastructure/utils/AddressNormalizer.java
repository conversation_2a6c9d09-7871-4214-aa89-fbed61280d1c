package com.mercaso.wms.infrastructure.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AddressNormalizer {

    private static final Map<String, String> SUFFIX_MAP = new HashMap<>();
    private static final Map<String, String> DIRECTION_MAP = new HashMap<>();
    private static final Map<String, String> STATE_MAP = new HashMap<>();

    static {
        SUFFIX_MAP.put("st", "Street");
        SUFFIX_MAP.put("st.", "Street");
        SUFFIX_MAP.put("rd", "Road");
        SUFFIX_MAP.put("rd.", "Road");
        SUFFIX_MAP.put("ave", "Avenue");
        SUFFIX_MAP.put("ave.", "Avenue");
        SUFFIX_MAP.put("blvd", "Boulevard");
        SUFFIX_MAP.put("blvd.", "Boulevard");
        SUFFIX_MAP.put("ln", "Lane");
        SUFFIX_MAP.put("ln.", "Lane");
        SUFFIX_MAP.put("dr", "Drive");
        SUFFIX_MAP.put("dr.", "Drive");
        SUFFIX_MAP.put("ct", "Court");
        SUFFIX_MAP.put("ct.", "Court");
        SUFFIX_MAP.put("cir", "Circle");
        SUFFIX_MAP.put("cir.", "Circle");
        SUFFIX_MAP.put("pl", "Place");
        SUFFIX_MAP.put("pl.", "Place");
        SUFFIX_MAP.put("ter", "Terrace");
        SUFFIX_MAP.put("ter.", "Terrace");
        SUFFIX_MAP.put("pkwy", "Parkway");
        SUFFIX_MAP.put("pkwy.", "Parkway");
        SUFFIX_MAP.put("hwy", "Highway");
        SUFFIX_MAP.put("hwy.", "Highway");
        SUFFIX_MAP.put("sq", "Square");
        SUFFIX_MAP.put("sq.", "Square");
        SUFFIX_MAP.put("trl", "Trail");
        SUFFIX_MAP.put("trl.", "Trail");

        DIRECTION_MAP.put("n", "North");
        DIRECTION_MAP.put("n.", "North");
        DIRECTION_MAP.put("s", "South");
        DIRECTION_MAP.put("s.", "South");
        DIRECTION_MAP.put("e", "East");
        DIRECTION_MAP.put("e.", "East");
        DIRECTION_MAP.put("w", "West");
        DIRECTION_MAP.put("w.", "West");
        DIRECTION_MAP.put("ne", "Northeast");
        DIRECTION_MAP.put("ne.", "Northeast");
        DIRECTION_MAP.put("nw", "Northwest");
        DIRECTION_MAP.put("nw.", "Northwest");
        DIRECTION_MAP.put("se", "Southeast");
        DIRECTION_MAP.put("se.", "Southeast");
        DIRECTION_MAP.put("sw", "Southwest");
        DIRECTION_MAP.put("sw.", "Southwest");

        STATE_MAP.put("NY", "New York");
        STATE_MAP.put("CA", "California");
        STATE_MAP.put("TX", "Texas");
        STATE_MAP.put("FL", "Florida");
        STATE_MAP.put("IL", "Illinois");
    }

    public static String normalize(String raw) {
        if (raw == null) {
            return null;
        }

        String s = raw.trim();
        // First, filter out special characters that would create empty tokens, but preserve some important ones
        s = s.replaceAll("[^A-Za-z0-9#\\s,\\-.]", " ");
        s = s.replaceAll("\\s+", " ");
        s = s.replaceAll("\\s*,\\s*", ", ");

        List<String> tokens = new ArrayList<>();
        Matcher m = Pattern.compile("[^\\s,]+|,").matcher(s);
        while (m.find()) {
            String token = m.group();
            if (",".equals(token)) {
                tokens.add(token);
            } else if (!token.trim().isEmpty()) {
                tokens.add(token);
            }
        }

        for (int i = 0; i < tokens.size(); i++) {
            String tok = tokens.get(i);
            if (",".equals(tok)) {
                continue;
            }

            String plain = tok.replaceAll("[^A-Za-z0-9#\\-]", "");
            String lower = plain.toLowerCase();

            if (DIRECTION_MAP.containsKey(lower)) {
                tokens.set(i, DIRECTION_MAP.get(lower));
                continue;
            }

            if (SUFFIX_MAP.containsKey(lower)) {
                tokens.set(i, SUFFIX_MAP.get(lower));
                continue;
            }

            if (plain.length() == 2 && plain.equals(plain.toUpperCase()) && STATE_MAP.containsKey(plain)) {
                tokens.set(i, STATE_MAP.get(plain));
                continue;
            }

            String upper = plain.toUpperCase();
            if ("PO".equals(upper) || "P.O.".equals(upper)) {
                if (i + 1 < tokens.size()) {
                    String next = tokens.get(i + 1).replaceAll("\\W", "").toUpperCase();
                    if ("BOX".equals(next)) {
                        tokens.set(i, "PO Box");
                        tokens.remove(i + 1);
                        continue;
                    }
                }
                tokens.set(i, "PO");
                continue;
            }
            if ("APT".equals(upper) || "UNIT".equals(upper) || "STE".equals(upper) || "SUITE".equals(upper)) {
                tokens.set(i, upper.charAt(0) + upper.substring(1).toLowerCase());
                continue;
            }

            tokens.set(i, titleCase(plain));
        }

        StringBuilder out = new StringBuilder();
        for (String tkn : tokens) {
            if (",".equals(tkn)) {
                if (!out.isEmpty() && out.charAt(out.length() - 1) == ' ') {
                    out.setLength(out.length() - 1);
                }
                out.append(", ");
            } else {
                if (!out.isEmpty() && !out.toString().endsWith(", ")) {
                    out.append(' ');
                }
                out.append(tkn);
            }
        }

        String result = out.toString().trim();
        result = result.replaceAll("\\s+,", ",");
        result = result.replaceAll(",\\s*$", "");
        return result;
    }

    private static String titleCase(String word) {
        if (word.isEmpty()) {
            return word;
        }
        if (word.matches("^\\d+$")) {
            return word;
        }
        if (word.matches(".*\\d.*")) {
            return word.toUpperCase();
        }
        String lower = word.toLowerCase();
        return Character.toUpperCase(lower.charAt(0)) + lower.substring(1);
    }

}
