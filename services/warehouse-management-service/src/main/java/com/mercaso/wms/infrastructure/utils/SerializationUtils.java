package com.mercaso.wms.infrastructure.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.List;
import java.util.Locale;
import lombok.SneakyThrows;

public class SerializationUtils {

    private SerializationUtils() {
    }

    public static ObjectMapper standardObjectMapper() {
        ObjectMapper mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        SimpleModule module = new SimpleModule();
        module.addDeserializer(LocalDateTime.class, new CustomLocalDateTimeDeserializer());
        mapper.registerModule(module);
        return mapper;
    }

    private static class CustomLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

        private static final DateTimeFormatter FORMATTER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .appendPattern("MMM d yyyy h:mm:ss a")
            .toFormatter(Locale.ENGLISH);

        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateString = p.getText().trim();
            try {
                return LocalDateTime.parse(dateString, FORMATTER);
            } catch (Exception e) {
                throw new IOException("Error parsing date: " + dateString, e);
            }
        }
    }

    private static final ObjectMapper objectMapper = standardObjectMapper();

    public static <S> S deserialize(String json, Class<S> domainClass) throws IOException {
        // Special handling for String class to support plain text responses (like Slack "ok")
        if (domainClass == String.class) {
            try {
                return objectMapper.readValue(json, domainClass);
            } catch (Exception e) {
                // If JSON parsing fails and expecting String, return the raw text
                // This handles cases like: plain text responses, empty strings, etc.
                @SuppressWarnings("unchecked")
                S result = (S) json;
                return result;
            }
        }
        return objectMapper.readValue(json, domainClass);
    }

    public static String serialize(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new WmsBusinessException("serialize exception", e);
        }
    }

    public static JsonNode toTree(Object object) {
        return objectMapper.valueToTree(object);
    }

    public static <S> S fromTree(JsonNode json, Class<S> domainClass) {
        try {
            return objectMapper.treeToValue(json, domainClass);
        } catch (Exception e) {
            throw new WmsBusinessException("treeToValue exception", e);
        }
    }

    @SneakyThrows
    public static <T> List<T> fromTreeToList(JsonNode json, TypeReference<List<T>> typeRef) {
        ObjectReader reader = objectMapper.readerFor(typeRef);
        return reader.readValue(json);

    }

    public static <T> T readValue(String content, TypeReference<T> valueTypeRef) throws IOException {
        return objectMapper.readValue(content, valueTypeRef);
    }

}
