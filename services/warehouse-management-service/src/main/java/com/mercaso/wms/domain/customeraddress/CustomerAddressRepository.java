package com.mercaso.wms.domain.customeraddress;

import com.mercaso.wms.domain.BaseDomainRepository;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

public interface CustomerAddressRepository extends BaseDomainRepository<CustomerAddress, UUID> {

    CustomerAddress findBySha1hex(String sha1hex);

    List<CustomerAddress> findByLatitudeAndLongitude(BigDecimal latitude, BigDecimal longitude);

    List<CustomerAddress> findByCityAndAddressOne(String city, String addressOne);

    List<CustomerAddress> findByEmail(String email);

}
