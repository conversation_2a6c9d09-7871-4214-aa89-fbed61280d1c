package com.mercaso.wms.infrastructure.repository.customer.jpa;

import com.mercaso.wms.infrastructure.repository.customer.jpa.dataobject.CustomerAddressDo;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface CustomerAddressJpaDao extends JpaRepository<CustomerAddressDo, UUID> {

    CustomerAddressDo findBySha1hex(String sha1hex);

    List<CustomerAddressDo> findByLatitudeAndLongitude(BigDecimal latitude, BigDecimal longitude);

    List<CustomerAddressDo> findByCityAndAddressOneIgnoreCase(String city, String addressOne);

    List<CustomerAddressDo> findByEmail(String email);

}
